import React from "react";
import Select from "react-select";
import { useField } from "formik";

const InputSelect = ({
  label,
  options = [],
  valueKey = "value",
  labelKey = "label",
  required = false,
  onChange,
  value,
  isLoading = false,
  placeholder = "Select...",
  name,
  ...props
}) => {
  // Support both Formik and non-Formik usage
  let field, meta, helpers;
  let isFormik = !!name;
  if (isFormik) {
    [field, meta, helpers] = useField({ name, ...props });
  }
  const isError = isFormik && meta.touched && meta.error;

  const handleChange = (selectedOption) => {
    if (onChange) {
      onChange(selectedOption);
    } else if (isFormik) {
      helpers.setValue(selectedOption.value);
    }
  };

  const formattedOptions = options.map((option) => ({
    value: option[valueKey],
    label: option[labelKey],
  }));

  const handleKeyup = (selectedOption) => {};

  // Determine value for controlled or Formik mode
  const selectValue = value !== undefined
    ? formattedOptions.find((opt) => opt.value === value) || null
    : isFormik
      ? formattedOptions.find((opt) => opt.value === field.value) || null
      : null;

  return (
    <div className="w-full">
      {label && (
        <label
          className="inline-block text-black-900 text-base mb-2 capitalize"
          htmlFor={props.id || name}
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <Select
        value={selectValue}
        options={formattedOptions}
        onChange={handleChange}
        onKeyUp={handleKeyup}
        onBlur={isFormik ? () => helpers.setTouched(true) : undefined}
        isLoading={isLoading}
        placeholder={placeholder}
        classNamePrefix="react-select"
        className={`react-select-container rounded-md ${isError ? "border-red-500 border" : "border border-gray-300"}`}
        styles={{
          control: (base, state) => ({
            ...base,
            boxShadow: 'none',
            border: 'none',
            '&:hover': {
              border: 'none'
            }
          }),
          input: (base) => ({
            ...base,
            minHeight: "40px",
          }),
          valueContainer: (base) => ({
            ...base,
            minHeight: "40px",
            display: "flex",
            alignItems: "center",
          }),
          container: (base) => ({
            ...base,
            minHeight: "50px",
          }),
        }}
      />

      {isFormik && field.touched && field.error ? <div>{field.error}</div> : null}
    </div>
  );
};

export default InputSelect;
