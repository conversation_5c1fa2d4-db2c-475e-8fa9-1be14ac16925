import { createSlice } from "@reduxjs/toolkit";
import { set } from "react-hook-form";
import { v4 as uuidv4 } from "uuid";
import Cookies from "js-cookie";
import { setCookieWithExpiry } from "@/helper/helperFunction";
const storedUser = JSON.parse(localStorage.getItem("user"));
const initialIsAuth = () => {
  const item = Cookies.get("isAuth");
  return item ? JSON.parse(item) : false;
};
export const authSlice = createSlice({
  name: "auth",
  initialState: {
    user: storedUser || null,
    isAuth: initialIsAuth(),
    userCategory: null
  },
  reducers: {
    setUser: (state, action) => {
      state.user = action.payload;
      state.isAuth = true;
    },
    logOut: (state, action) => {
      state.user = null;
      state.isAuth = false;
      Cookies.remove("isAuth");
      Cookies.remove("_token");
      Cookies.remove("userInfo");
    },
    handleLogin: (state, action) => {
      Cookies.set("_token", action?.payload?.data?.token , { expires: setCookieWithExpiry(action?.payload?.data?.expires_in) });
      Cookies.set("isAuth", true , { expires: setCookieWithExpiry(action?.payload?.data?.expires_in) });
      Cookies.set("userInfo", JSON.stringify(action?.payload?.data?.user) , { expires: setCookieWithExpiry(action?.payload?.data?.expires_in) });
      state.isAuth = true;
    },
    handleResetPassword: (state, action) => {
      Cookies.set("_token", action?.payload?.data?.token , { expires: setCookieWithExpiry(action?.payload?.data?.expires_in) });
      Cookies.set("isAuth", true , { expires: setCookieWithExpiry(action?.payload?.data?.expires_in) });
      Cookies.set("userInfo", JSON.stringify(action?.payload?.data?.user) , { expires: setCookieWithExpiry(action?.payload?.data?.expires_in) });
    },
  },
});

export const { setUser, logOut, handleLogin, handleResetPassword } = authSlice.actions;
export default authSlice.reducer;
