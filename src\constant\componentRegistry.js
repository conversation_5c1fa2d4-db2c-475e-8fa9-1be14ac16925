import BulkUsersUpload from '@/components/teachersStudents/BulkUsersUpload';
import SingleSchoolsForm from '@/components/teachersStudents/SingleSchoolsForm';
import SingleStudentCreateForm from '@/components/teachersStudents/SingleStudentCreateForm';
import SingleTeacherCreateForm from '@/components/teachersStudents/SingleTeacherCreateForm';

export const componentRegistry = {
    uploadbulkStudnts: BulkUsersUpload,
    addSingleStudnts: SingleStudentCreateForm,
    updateSingleStudnts: SingleStudentCreateForm,
    addSingleSchool: SingleSchoolsForm,
    updateSingleSchool: SingleSchoolsForm,
    addSingleTeacher: SingleTeacherCreateForm,
    updateSingleTeacher: SingleTeacherCreateForm,
}

