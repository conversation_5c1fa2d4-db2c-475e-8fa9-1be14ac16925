import React from "react";
// import images
import Twitter from "https://images.ctfassets.net/cnu0m8re1exe/2q9faCtEKBw41JgsRhLngs/09bb62cd310d5058d7a6eac1ffc99ff1/circle.jpg?fm=jpg&fl=progressive&w=660&h=433&fit=fill";
import FaceBook from "https://images.ctfassets.net/cnu0m8re1exe/2q9faCtEKBw41JgsRhLngs/09bb62cd310d5058d7a6eac1ffc99ff1/circle.jpg?fm=jpg&fl=progressive&w=660&h=433&fit=fill";
import LinkedIn from "https://images.ctfassets.net/cnu0m8re1exe/2q9faCtEKBw41JgsRhLngs/09bb62cd310d5058d7a6eac1ffc99ff1/circle.jpg?fm=jpg&fl=progressive&w=660&h=433&fit=fill";
import Google from "https://images.ctfassets.net/cnu0m8re1exe/2q9faCtEKBw41JgsRhLngs/09bb62cd310d5058d7a6eac1ffc99ff1/circle.jpg?fm=jpg&fl=progressive&w=660&h=433&fit=fill";

const Social = () => {
  return (
    <ul className="flex">
      <li className="flex-1">
        <a
          href="#"
          className="inline-flex h-10 w-10 bg-[#1C9CEB] text-white text-2xl flex-col items-center justify-center rounded-full"
        >
          <img src={Twitter} alt="" />
        </a>
      </li>
      <li className="flex-1">
        <a
          href="#"
          className="inline-flex h-10 w-10 bg-[#395599] text-white text-2xl flex-col items-center justify-center rounded-full"
        >
          <img src={FaceBook} alt="" />
        </a>
      </li>
      <li className="flex-1">
        <a
          href="#"
          className="inline-flex h-10 w-10 bg-[#0A63BC] text-white text-2xl flex-col items-center justify-center rounded-full"
        >
          <img src={LinkedIn} alt="" />
        </a>
      </li>
      <li className="flex-1">
        <a
          href="#"
          className="inline-flex h-10 w-10 bg-[#EA4335] text-white text-2xl flex-col items-center justify-center rounded-full"
        >
          <img src={Google} alt="" />
        </a>
      </li>
    </ul>
  );
};

export default Social;
