import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";

const usePagination = (endpoint, apiCall) => {
    const [searchParams, setSearchParams] = useSearchParams();
    const [currentPage, setCurrentPage] = useState(
        Number(searchParams.get("page")) || 1
    );
    const [itemPerPage, setItemPerPage] = useState(
        Number(searchParams.get("itemsPerPage")) || 10
    );

    const { data, error, isLoading, refetch } = apiCall(`${endpoint}?page=${currentPage}&itemsPerPage=${itemPerPage}`);

    const totalPages = data?.data?.last_page || 1;

    // useEffect(() => {
    //     setSearchParams({ page: currentPage });
    // }, [currentPage, setSearchParams]);

    const handlePageChange = (newPage) => {
        if (newPage >= 1 && newPage <= totalPages) {
            setCurrentPage(newPage);
        }
    };    

    const handleItemPerPage = (newItemsPerPage) => {
        // setSearchParams({ page: currentPage, itemsPerPage: newItemsPerPage });
        setItemPerPage(newItemsPerPage);
    };


    const handleReset = () => {
        setCurrentPage(1);
        refetch();
    }

    return { data: data || [], currentPage, totalPages, handlePageChange, handleReset, isLoading, error, handleItemPerPage };
};

export default usePagination;
