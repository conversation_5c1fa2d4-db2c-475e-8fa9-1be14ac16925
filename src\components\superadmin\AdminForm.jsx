import React from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import InputField from "@/components/ui/formik-form/InputField";
import ProfileImageUploadFormik from "@/components/ui/formik-form/ProfileImageUploadFormik";
import Button from "@/components/ui/Button";

const validationSchema = Yup.object().shape({
  name: Yup.string().required("Admin name is required"),
  name_jp: Yup.string(),
  email: Yup.string().email("Invalid email").required("Email is required"),
  password: Yup.string().required("Password is required"),
  phone: Yup.string().required("Contact No. is required"),
  school_name: Yup.string(),
  address: Yup.string(),
  avatar: Yup.mixed(),
});

const getInitialValues = (adminData) => ({
  name: adminData?.name || "",
  name_jp: adminData?.name_jp || "",
  email: adminData?.email || "",
  password: "",
  phone: adminData?.phone || "",
  school_name: adminData?.school_name || "",
  address: adminData?.address || "",
  avatar: null,
});

const AdminForm = ({ adminData, isUpdateMode = false, onSuccess }) => {
  return (
    <Formik
      initialValues={getInitialValues(adminData)}
      validationSchema={validationSchema}
      onSubmit={async (values, { setSubmitting }) => {
        setSubmitting(true);
        // Simulate API call
        setTimeout(() => {
          setSubmitting(false);
          if (onSuccess) onSuccess(values);
        }, 800);
      }}
    >
      {({ isSubmitting, setFieldValue, values }) => (
        <Form>
          <div className="p-6">
            <h2 className="text-lg font-semibold mb-6">{isUpdateMode ? "Update Admin" : "Create Admin"}</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex flex-col gap-4">
                <InputField
                  label="Admin name"
                  name="name"
                  required
                  placeholder="Enter admin name"
                />
                <InputField
                  label="Email Address"
                  name="email"
                  type="email"
                  required
                  placeholder="Enter email"
                />
                <InputField
                  label="Contact No."
                  name="phone"
                  required
                  placeholder="Enter contact no."
                />
                <div>
                  <label className="inline-block text-black-900 text-base mb-2">Admin Address</label>
                  <textarea
                    name="address"
                    className="appearance-none border rounded-md w-full px-4 py-[14px] text-base text-black-900 leading-tight focus:outline-none focus:-outline placeholder:text-sm min-h-[70px]"
                    placeholder="Admin full address"
                    value={values.address}
                    onChange={e => setFieldValue("address", e.target.value)}
                  />
                </div>
              </div>
              <div className="flex flex-col gap-4">
                <InputField
                  label="Admin Japanese name"
                  name="name_jp"
                  placeholder="Enter Admin Japanese name"
                />
                <InputField
                  label="Password"
                  name="password"
                  type="password"
                  required
                  placeholder="Enter password"
                />
                <InputField
                  label="School Name"
                  name="school_name"
                  placeholder="School Name"
                />
                <div>
                  <label className="inline-block text-black-900 text-base mb-2">Upload Photo</label>
                  <ProfileImageUploadFormik
                    name="avatar"
                    imgData={values.avatar}
                    uploadImage={file => setFieldValue("avatar", file)}
                  />
                </div>
              </div>
            </div>
            <div className="flex justify-end mt-6">
              <Button
                type="submit"
                className="bg-orange-500 text-white px-6 py-2 rounded hover:bg-orange-600"
                isLoading={isSubmitting}
              >
                Submit
              </Button>
            </div>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default AdminForm; 