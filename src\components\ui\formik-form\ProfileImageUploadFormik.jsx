import React, { useEffect, useRef, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Icon } from '@iconify/react';
import { useField } from 'formik';
import { replaceUploadUrl } from '@/helper/helperFunction';

const ProfileImageUploadFormik = ({ imgData, name, required = false, uploadImage }) => {
  const [field, meta, helpers] = useField(name);
  const isError = !field.value && meta.touched && meta.error;
  const [imgDataUrl, setImgDataUrl] = useState(null);
  const fileInputRef = useRef(null);
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    if (imgData) {
      // Check if imgData is a string before using split
      if (typeof imgData === 'string') {
        const isBlob = imgData.includes('blob');
        if (isBlob) {
          setImgDataUrl(imgData);
        } else {
          setImgDataUrl(replaceUploadUrl(imgData));
        }
      } else if (imgData instanceof File) {
        // If imgData is a File object, create a URL for it
        const previewUrl = URL.createObjectURL(imgData);
        setImgDataUrl(previewUrl);
      } else if (imgData instanceof Blob) {
        // If imgData is a Blob, create a URL for it
        const previewUrl = URL.createObjectURL(imgData);
        setImgDataUrl(previewUrl);
      }
    } else {
      setImgDataUrl(null);
    }
  }, [imgData]);
  
  

  const onDrop = (acceptedFiles) => {
    if (acceptedFiles?.length) {
      const file = acceptedFiles[0];
      
      // Set form field value immediately
      helpers.setValue(file);
      helpers.setTouched(true);
      
      // Create and set preview URL immediately
      const previewUrl = URL.createObjectURL(file);
      setImgDataUrl(previewUrl);
      
      // Handle the actual upload in a non-blocking way
      if (uploadImage) {
        setIsUploading(true);
        
        // Use Promise to handle the upload asynchronously
        Promise.resolve()
          .then(() => {
            uploadImage(file);
          })
          .finally(() => {
            setIsUploading(false);
          });
      }
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png'],
    },
    maxFiles: 1,
    multiple: false,
    disabled: isUploading, // Disable dropzone while uploading
  });

  const handleDelete = () => {
    setImgDataUrl(null);
    helpers.setValue(null);
    
    // Also notify parent component about deletion
    if (uploadImage) {
      uploadImage(null);
    }
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    
    if (file) {
      // Set form field value immediately
      helpers.setValue(file);
      helpers.setTouched(true);
      
      // Create and set preview URL immediately
      const previewUrl = URL.createObjectURL(file);
      setImgDataUrl(previewUrl);
      
      // Handle the actual upload in a non-blocking way
      if (uploadImage) {
        setIsUploading(true);
        
        // Use Promise to handle the upload asynchronously
        Promise.resolve()
          .then(() => {
            uploadImage(file);
          })
          .finally(() => {
            setIsUploading(false);
          });
      }
    }
  };
  

  return (
    <div className="mb-6">
      <label className="block text-sm font-medium text-gray-700 mb-1 hidden">
        PROFILE PHOTO {required && <span className="text-red-500">*</span>}
      </label>

      {imgDataUrl ? (
        <div
          className={`border border-dashed ${
            isError ? 'border-red-500' : 'border-slate-200 dark:border-slate-700'
          } rounded-lg p-4`}
        >
          <div className="flex items-end gap-4 justify-between flex-wrap">
            <div className="flex items-center flex-col gap-4 max-w-[25%]">
              <div className="h-[100px] w-[100px] rounded-full overflow-hidden border-4 border-slate-100 relative">
                <img
                  src={imgDataUrl}
                  alt="Profile"
                  className="h-[100px] w-[100px] object-cover"
                />
                {isUploading && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></div>
                  </div>
                )}
              </div>
            </div>

            <div>
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                accept="image/*"
                onChange={handleFileChange}
                disabled={isUploading}
              />

              <div className="flex gap-2 justify-between items-baseline">
                <button
                  type="button"
                  className={`px-4 py-2 text-sm text-blue-600 rounded-lg flex items-center gap-2 border border-blue-600 hover:border-blue-200 ${
                    isUploading ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                  onClick={() => fileInputRef.current.click()}
                  disabled={isUploading}
                >
                  <Icon icon="heroicons:arrow-path" className="w-4 h-4" />
                  Change
                </button>

                <button
                  type="button"
                  onClick={handleDelete}
                  className={`px-4 py-2 text-sm text-red-600 rounded-lg flex items-center gap-2 border border-red-600 hover:border-red-200 ${
                    isUploading ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                  disabled={isUploading}
                >
                  <Icon icon="heroicons:trash" className="w-4 h-4" />
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="mt-3">
          <div
            {...getRootProps()}
            className={`border-2 border-dashed ${
              isDragActive
                ? 'border-primary-500 bg-primary-50'
                : 'border-slate-200 dark:border-slate-700'
            } rounded-md p-5 transition-all duration-300 ease-in-out cursor-pointer hover:border-primary-500 ${
              isError ? 'border-red-500' : ''
            } ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <input {...getInputProps()} disabled={isUploading} />
            <div className="flex flex-col gap-5 justify-center items-center">
              {isUploading ? (
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
              ) : (
                <>
                  <div>
                    <Icon
                      icon="icons8:upload-2"
                      width="32"
                      height="32"
                      className={isDragActive ? 'text-primary-500' : ''}
                    />
                  </div>
                  <div>
                    <span className="text-sm text-blue-400">Click to upload</span>
                    <span className="text-sm text-black dark:text-white ml-1">or</span>
                    <span className="text-sm text-black dark:text-white ml-1">drag and drop</span>
                  </div>
                </>
              )}
            </div>
          </div>
          <div className="mt-2">
            <span className="text-secondary-300 text-xs">
              Accepted formats: JPEG, JPG, PNG (max 5MB)
            </span>
          </div>
        </div>
      )}

      {isError && <div className="text-red-500 text-base mt-1">{meta.error}</div>}
    </div>
  );
};

export default ProfileImageUploadFormik;
