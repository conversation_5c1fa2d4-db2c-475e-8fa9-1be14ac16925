import React, { useState, useEffect } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import FloadingInputField from "@/components/ui/formik-form/FloadingInputField";
import FloadingTextarea from "@/components/ui/formik-form/FloadingTextarea";
import ProfileImageUploadFormik from "@/components/ui/formik-form/ProfileImageUploadFormik";
import Table from "@/components/ui/table/Table";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { Icon } from "@iconify/react";
import { useFetchDataQuery, usePostDataWithFileMutation, usePutDataWithFileMutation, useDeleteDataMutation } from "@/store/api/apiSlice";
import { toast } from "react-toastify";

const NOTICE_API = "/notice-boards";

const NoticeBoard = () => {
  const [showModal, setShowModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [viewNotice, setViewNotice] = useState(null);
  const [editNotice, setEditNotice] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // API hooks
  const { data, refetch, isLoading } = useFetchDataQuery(NOTICE_API);
  const [postNotice, { isLoading: isPosting }] = usePostDataWithFileMutation();
  const [putNotice, { isLoading: isPutting }] = usePutDataWithFileMutation();
  const [deleteNotice] = useDeleteDataMutation();

  // Notices from API
  const notices = Array.isArray(data?.data?.data) ? data.data.data : [];

  // Filtered notices for search
  const filteredNotices = notices.filter(
    (notice) =>
      notice.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notice.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalPages = Math.ceil(filteredNotices.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedNotices = filteredNotices.slice(startIndex, startIndex + itemsPerPage);

  const paginationData = {
    currentPage,
    perPage: itemsPerPage,
    total: totalPages,
    totalRecords: filteredNotices.length,
    dataFrom: startIndex + 1,
    dataTo: Math.min(startIndex + itemsPerPage, filteredNotices.length)
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleItemPerPage = (perPage) => {
    setItemsPerPage(perPage);
    setCurrentPage(1);
  };

  const handleDelete = async (notice) => {
    if (window.confirm("Are you sure you want to delete this notice?")) {
      try {
        await deleteNotice({ url: `${NOTICE_API}/${notice.id}` }).unwrap();
        toast.success("Notice deleted successfully");
        refetch();
      } catch (error) {
        toast.error(error?.data?.message || "Failed to delete notice");
      }
    }
  };

  const columns = [
    {
      header: "#",
      accessorKey: "index",
      cell: (info) => info.row.index + 1,
    },
    {
      header: "TITLE",
      accessorKey: "title",
      cell: (info) => {
        const value = info.getValue();
        return (
          <span
            className="block max-w-[180px] truncate cursor-pointer"
            title={value}
          >
            {value}
          </span>
        );
      },
    },
    {
      header: "DESCRIPTION",
      accessorKey: "description",
      cell: (info) => {
        const value = info.getValue();
        return (
          <span
            className="block max-w-[260px] truncate cursor-pointer"
            title={value}
          >
            {value}
          </span>
        );
      },
    },
    {
      header: "ACTIONS",
      id: "actions",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <button
            className="w-8 h-8 flex items-center justify-center rounded-md bg-white shadow border border-gray-100 hover:bg-gray-50"
            onClick={() => setEditNotice({ ...row.original })}
          >
            <Icon icon="heroicons:pencil-square" className="text-slate-400" />
          </button>
          <button
            className="w-8 h-8 flex items-center justify-center rounded-md bg-white shadow border border-gray-100 hover:bg-gray-50"
            onClick={() => setViewNotice(row.original)}
          >
            <Icon icon="heroicons:eye" className="text-slate-400" />
          </button>
          <button
            className="w-8 h-8 flex items-center justify-center rounded-md bg-white shadow border border-gray-100 hover:bg-gray-50 hidden"
            onClick={() => handleDelete(row.original)}
          >
            <Icon icon="heroicons:trash" className="text-slate-400" />
          </button>
        </div>
      ),
    },
  ];

  return (
    <div className="page-content page-min-height p-0">
      <div className="max-w-10xl mx-auto mt-8">
        <div className="border shadow-sm rounded-md overflow-hidden bg-white">
          <div className="mb-6 p-6 pb-0">
            <div className="flex flex-wrap items-center justify-between gap-4">
              <h4 className="font-medium text-xl text-slate-900">Notice List</h4>
              <div className="flex items-center gap-3">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search..."
                    className="pl-8 pr-4 py-2 border rounded-md"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <Icon icon="heroicons-outline:search" className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                </div>
                <Button
                  className="bg-orange-400 text-white px-4 py-2 rounded-md hover:bg-orange-600 transition duration-300"
                  onClick={() => setShowModal(true)}
                  icon="heroicons-outline:plus"
                >
                  Add Notice
                </Button>
              </div>
            </div>
          </div>
          <div className="p-6 pt-0">
            <Table 
              columns={columns} 
              data={paginatedNotices} 
              isLoading={isLoading}
              dynamicPagination={true}
              paginationData={paginationData}
              handlePageChange={handlePageChange}
              handleItemPerPage={handleItemPerPage}
            />
          </div>
        </div>
      </div>
      {/* Add Notice Modal */}
      <Modal activeModal={showModal} onClose={() => setShowModal(false)} title="Add Notice" centered>
        <Formik
          initialValues={{ title: "", description: "", image: null }}
          validationSchema={Yup.object({
            title: Yup.string().required("Title is required"),
            description: Yup.string().required("Description is required"),
            image: Yup.mixed().nullable(),
          })}
          onSubmit={async (values, { resetForm }) => {
            const formData = new FormData();
            formData.append("title", values.title);
            formData.append("description", values.description);
            if (values.attachment ) formData.append("image", values.attachment );
            try {
              await postNotice({ url: NOTICE_API, body: formData }).unwrap();
              toast.success("Notice added successfully");
              resetForm();
              setShowModal(false);
              refetch();
            } catch (error) {
              toast.error(error?.data?.message || "Failed to add notice");
            }
          }}
        >
          {({ setFieldValue, values }) => {
            console.log('Formik values:', values);
            return (
              <Form className="space-y-4 bg-white p-4 rounded shadow">
                <FloadingInputField
                  label="Title"
                  name="title"
                  placeholder="Enter notice title"
                />
                <FloadingTextarea
                  label="Description"
                  name="description"
                  required
                  placeholder="Enter notice description"
                />
                {/* <ProfileImageUploadFormik
                  name="image"
                  imgData={values.attachment  }
                  uploadImage={(file) => setFieldValue("image", file)}
                /> */}
                <div className="flex justify-end">
                  <Button type="submit" className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700" isLoading={isPosting}>
                    Submit
                  </Button>
                </div>
              </Form>
            );
          }}
        </Formik>
      </Modal>
      {/* View Notice Modal */}
      <Modal activeModal={!!viewNotice} onClose={() => setViewNotice(null)} title="Notice Details" centered className="max-w-5xl max-h-7xl" themeClass="bg-orange-300 text-white">
        {viewNotice && (
          <div className="flex flex-col items-center gap-4">
            {/* {viewNotice.attachment  && (
              <img
                src={viewNotice.attachment }
                alt="Notice"
                className="max-w-xs max-h-80 rounded border mb-4"
              />
            )} */}
            <h2 className="text-3xl font-extrabold mb-2 text-center text-primary-700 tracking-wide">
              {viewNotice.title}
            </h2>
            <div className="w-24 h-1 bg-primary-200 rounded-full mb-4"></div>
            <div className="w-full flex justify-center">
              <p className="text-gray-800 text-lg text-center whitespace-pre-line break-words max-w-3xl w-full px-6 py-4 bg-gray-50 rounded shadow-sm border border-gray-100" style={{wordBreak: 'break-word'}}>
                {viewNotice.description}
              </p>
            </div>
          </div>
        )}
      </Modal>
      {/* Edit Notice Modal */}
      <Modal activeModal={!!editNotice} onClose={() => setEditNotice(null)} title="Update Notice" centered themeClass="bg-orange-300 text-white">
        {editNotice && (
          <Formik
            enableReinitialize
            initialValues={{
              title: editNotice.title,
              description: editNotice.description,
              thumbnail: null,
            }}
            validationSchema={Yup.object({
              title: Yup.string().required("Title is required"),
              description: Yup.string().required("Description is required"),
              thumbnail: Yup.mixed().nullable(),
            })}
            onSubmit={async (values, { resetForm }) => {
              const payload = {
                title: values.title,
                description: values.description,
              };

              try {
                await putNotice({ url: `${NOTICE_API}/${editNotice.id}`, body: payload }).unwrap();
                toast.success("Notice updated successfully");
                resetForm();
                setEditNotice(null);
                refetch();
              } catch (error) {
                toast.error(error?.data?.message || "Failed to update notice");
              }
            }}
          >
            {({ setFieldValue, values }) => (
              <Form className="space-y-4 bg-white p-4 rounded shadow">
                <FloadingInputField
                  label="Title"
                  name="title"
                  placeholder="Enter notice title"
                />
                <FloadingTextarea
                  label="Description"
                  name="description"
                  required
                  placeholder="Enter notice description"
                />
                {/* <ProfileImageUploadFormik
                  name="thumbnail"
                  imgData={values.thumbnail}
                  uploadImage={(file) => setFieldValue("thumbnail", file)}
                /> */}
                <div className="flex justify-end">
                  <Button type="submit" className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700" isLoading={isPutting}>
                    Update
                  </Button>
                </div>
              </Form>
            )}
          </Formik>
        )}
      </Modal>
    </div>
  );
};

export default NoticeBoard; 