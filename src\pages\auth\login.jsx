import React, { useEffect } from "react";
import { Link } from "react-router-dom";
import LoginForm from "./common/login-form";
import useDarkMode from "@/hooks/useDarkMode";

// image import
import LogoWhite from "@/assets/images/logo/logo.svg";
import Logo from "@/assets/images/logo/logo.svg";
import LoginPageSlider from "./common/LoginPageSlider";

const login = () => {
  const [isDark] = useDarkMode();
  return (
      <div
        className="md:grid grid-cols-1 gap-8 rounded-md h-full"
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <div className="col-span-12 md:col-span-6">
          <div className="h-full flex flex-col justify-center p-10 shadow border bg-white">
            <div>
            <div className="mb-4 w-full flex justify-center">
              <Link to="/" className="block">
                <img src={isDark ? LogoWhite : Logo} alt="logo" />
              </Link>
            </div>
              <div className="mb-5">
                <h1 className="text-2xl font-bold text-dark mb-2 text-center">
                  Login
                </h1>
                <span className="text-dark text-base text-center">
                  Welcome back! Please enter your details
                </span>
              </div>
              <LoginForm />
            </div>
          </div>
        </div>
      </div>
  );
};

export default login;
