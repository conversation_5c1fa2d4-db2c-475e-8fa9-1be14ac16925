import React, { useState, useEffect, useRef } from "react";
import Icon from "@/components/ui/Icon";
import SimpleBar from "simplebar-react";
import { useGetSchoolAdminListQuery, useFetchDataQuery, usePostDataMutation } from "@/store/api/apiSlice";
import { replaceUploadUrl } from "@/helper/helperFunction";
import DefaultProfile from "@/assets/DefaultProfile.svg";
import Card from "@/components/ui/Card";
import { toast } from "react-toastify";

const Chat = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedAdmin, setSelectedAdmin] = useState(null);
  const [newMessage, setNewMessage] = useState("");
  const [messages, setMessages] = useState([]);
  const messagesEndRef = useRef(null);
  const chatContainerRef = useRef(null);
  
  // Fetch school admin list
  const { data: adminApiData, isLoading } = useGetSchoolAdminListQuery();
  const admins = adminApiData?.data || [];

  // API hooks for messages
  const { data: messagesData, refetch: refetchMessages } = 
    useFetchDataQuery(selectedAdmin ? `/messages/${selectedAdmin.id}` : null, {
      skip: !selectedAdmin
    });
  
  const [postMessage] = usePostDataMutation();

  // Filter admins based on search term
  const filteredAdmins = admins.filter(admin => 
    admin.name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Mock messages for demonstration
  const mockMessages = [
    { id: 1, sender: "them", message: "Hi there!", time: "10:00 AM" },
    { id: 2, sender: "me", message: "Hello! How can I help you today?", time: "10:02 AM" },
    { id: 3, sender: "them", message: "I have a question about my school.", time: "10:05 AM" },
    { id: 4, sender: "me", message: "Sure, I'd be happy to help. What's your question?", time: "10:07 AM" },
    { id: 5, sender: "them", message: "I want to know about the admission process.", time: "10:10 AM" },
    { id: 6, sender: "me", message: "The admission process starts in June. You'll need to fill out an application form and submit required documents.", time: "10:12 AM" },
    { id: 7, sender: "them", message: "What documents do I need?", time: "10:15 AM" },
    { id: 8, sender: "me", message: "You'll need birth certificate, previous school records, and ID proof.", time: "10:17 AM" },
    { id: 9, sender: "them", message: "Thank you for the information!", time: "10:20 AM" },
    { id: 10, sender: "me", message: "You're welcome! Let me know if you have any other questions.", time: "10:22 AM" }
  ];

  // Set messages when an admin is selected
  useEffect(() => {
    if (selectedAdmin) {
      // Use API data if available, otherwise fallback to mock data
      if (messagesData?.data) {
        // Get current user ID (you'll need to get this from your auth context or state)
        const currentUserId = 1; // Replace with actual current user ID
        
        // Process messages to determine sender
        const messagesWithSender = messagesData.data.map(msg => ({
          ...msg,
          sender: msg.sender_id === currentUserId ? "me" : "them",
          time: msg.time || new Date(msg.created_at || Date.now()).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        }));
        
        setMessages(messagesWithSender);
      } else {
        setMessages(mockMessages);
      }
      
      // Scroll to bottom after messages are loaded
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [selectedAdmin, messagesData]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (newMessage.trim() === "" || !selectedAdmin) return;
    
    // Get current user ID (you'll need to get this from your auth context or state)
    const currentUserId = 1; // Replace with actual current user ID
    
    // Add new message to the conversation (optimistic update)
    const newMsg = {
      id: Date.now(), // Temporary ID
      sender_id: currentUserId,
      receiver_id: selectedAdmin.id,
      message: newMessage,
      created_at: new Date().toISOString(),
      sender: "me",
      time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
    
    setMessages([...messages, newMsg]);
    const tempMessage = newMessage;
    setNewMessage("");
    
    try {
      await postMessage({ 
        url: '/messages', 
        body: {
          receiver_id: selectedAdmin.id,
          message: tempMessage
        }
      }).unwrap();
      
      refetchMessages();
    } catch (error) {
      toast.error("Failed to send message");
      console.error("Error sending message:", error);
    }
  };

  return (
    <div className="h-full">
      <Card title="Messages" className="h-full rounded-xl shadow-xl overflow-hidden" bodyClass="p-0">
        <div className="flex h-full bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-900">
          {/* School Admin List sidebar */}
          <div className="w-[320px] border-r border-slate-200 dark:border-slate-700 h-full flex flex-col bg-white dark:bg-slate-800 shadow-md">
            <div className="p-5 border-b border-slate-200 dark:border-slate-700 bg-gradient-to-r from-orange-50 to-orange-100 dark:from-slate-700 dark:to-slate-800">
              <h2 className="font-semibold text-xl text-slate-800 dark:text-white flex items-center">
                <Icon icon="heroicons-outline:chat-alt-2" className="mr-2 text-orange-500" />
                School Admin
              </h2>
              <div className="relative mt-4">
                <input
                  type="text"
                  className="form-input py-2.5 pl-10 pr-4 w-full border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-orange-200 focus:border-orange-500 bg-white dark:bg-slate-700 dark:text-white transition-all duration-200"
                  placeholder="Search school admin..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-slate-400 flex items-center justify-center">
                  <Icon icon="heroicons-outline:search" />
                </span>
              </div>
            </div>
            
            <div className="overflow-y-auto flex-grow">
              <h3 className="px-5 py-3 text-sm font-medium text-slate-500 dark:text-slate-300 bg-slate-50 dark:bg-slate-700/50 uppercase tracking-wider">School Admin List</h3>
              {isLoading ? (
                <div className="flex justify-center items-center h-20">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
                </div>
              ) : (
                <SimpleBar className="h-[calc(100vh-350px)]">
                  <ul className="divide-y divide-slate-100 dark:divide-slate-700">
                    {filteredAdmins.map((admin) => (
                      <li 
                        key={admin.id}
                        className={`p-4 hover:bg-orange-50 dark:hover:bg-slate-700/70 cursor-pointer transition-all duration-200 ${
                          selectedAdmin?.id === admin.id 
                            ? 'bg-orange-50 dark:bg-slate-700 border-l-4 border-orange-500' 
                            : ''
                        }`}
                        onClick={() => setSelectedAdmin(admin)}
                      >
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0 relative">
                            <img 
                              src={admin.avatar ? replaceUploadUrl(admin.avatar) : DefaultProfile} 
                              alt={admin.name || "Profile"} 
                              className="w-12 h-12 rounded-full object-cover border-2 border-white dark:border-slate-700 shadow-md"
                              onError={e => {
                                e.target.onerror = null;
                                e.target.src = DefaultProfile;
                              }}
                            />
                            <span className="absolute bottom-0 right-0 w-3.5 h-3.5 bg-green-500 rounded-full border-2 border-white dark:border-slate-700"></span>
                          </div>
                          <div className="flex-grow min-w-0">
                            <h4 className="text-sm font-semibold text-slate-900 dark:text-white truncate">
                              {admin.name || "N/A"}
                            </h4>
                            <p className="text-xs text-slate-500 dark:text-slate-400 truncate mt-0.5">
                              {admin.email || "N/A"}
                            </p>
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </SimpleBar>
              )}
            </div>
          </div>

          {/* Chat area */}
          {selectedAdmin ? (
            <div className="flex-grow flex flex-col h-full bg-white dark:bg-slate-900 rounded-r-xl overflow-hidden">
              {/* Chat header */}
              <div className="p-4 border-b border-slate-200 dark:border-slate-700 flex items-center justify-between bg-white dark:bg-slate-800 shadow-sm backdrop-blur-sm bg-opacity-90 dark:bg-opacity-90">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <img 
                      src={selectedAdmin.avatar ? replaceUploadUrl(selectedAdmin.avatar) : DefaultProfile} 
                      alt={selectedAdmin.name || "Profile"} 
                      className="w-12 h-12 rounded-full object-cover border-2 border-white dark:border-slate-700 shadow-md"
                      onError={e => {
                        e.target.onerror = null;
                        e.target.src = DefaultProfile;
                      }}
                    />
                    <span className="absolute bottom-0 right-0 w-3.5 h-3.5 bg-green-500 rounded-full border-2 border-white dark:border-slate-700"></span>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-slate-900 dark:text-white">
                      {selectedAdmin.name || "N/A"}
                    </h4>
                    <p className="text-xs text-slate-500 dark:text-slate-400 flex items-center">
                      Online now
                    </p>
                  </div>
                </div>
                <div className="flex space-x-2 hidden">
                  <button className="p-2.5 rounded-full hover:bg-orange-50 dark:hover:bg-slate-700 transition-colors duration-200">
                    <Icon icon="heroicons-outline:phone" className="text-orange-500 text-xl" />
                  </button>
                  <button className="p-2.5 rounded-full hover:bg-orange-50 dark:hover:bg-slate-700 transition-colors duration-200">
                    <Icon icon="heroicons-outline:video-camera" className="text-orange-500 text-xl" />
                  </button>
                  <button className="p-2.5 rounded-full hover:bg-orange-50 dark:hover:bg-slate-700 transition-colors duration-200">
                    <Icon icon="heroicons-outline:dots-vertical" className="text-orange-500 text-xl" />
                  </button>
                </div>
              </div>
              
              {/* Messages - this is the scrollable area */}
              <div 
                className="flex-grow p-6 bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-900 overflow-y-auto"
                style={{ height: "calc(100vh - 350px)" }}
                ref={chatContainerRef}
              >
                <div className="space-y-6 max-w-3xl mx-auto">
                  {messages.map((msg) => (
                    <div 
                      key={msg.id} 
                      className={`flex ${msg.sender === 'me' ? 'justify-end' : 'justify-start'}`}
                    >
                      {msg.sender !== 'me' && (
                        <div className="flex-shrink-0 mr-3">
                          <img 
                            src={selectedAdmin.avatar ? replaceUploadUrl(selectedAdmin.avatar) : DefaultProfile} 
                            alt="Profile" 
                            className="w-8 h-8 rounded-full object-cover border border-white dark:border-slate-700"
                          />
                        </div>
                      )}
                      <div 
                        className={`max-w-[75%] rounded-2xl p-4 shadow-md ${
                          msg.sender === 'me' 
                            ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-tr-none' 
                            : 'bg-white dark:bg-slate-700 text-slate-900 dark:text-white rounded-tl-none'
                        }`}
                      >
                        <p className="leading-relaxed">{msg.message}</p>
                        <span className={`text-xs block mt-2 ${
                          msg.sender === 'me' 
                            ? 'text-orange-100' 
                            : 'text-slate-500 dark:text-slate-400'
                        }`}>
                          {msg.time}
                        </span>
                      </div>
                      {msg.sender === 'me' && (
                        <div className="flex-shrink-0 ml-3">
                          <div className="w-8 h-8 rounded-full bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center text-orange-600 dark:text-orange-400 font-semibold text-sm">
                            ME
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>
              </div>
              
              {/* Message input - this stays fixed */}
              <div className="p-4 border-t border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 backdrop-blur-sm bg-opacity-90 dark:bg-opacity-90">
                <form onSubmit={handleSendMessage} className="flex items-center space-x-3">
                  <button type="button" className="p-3 text-slate-400 hover:text-orange-500 hover:bg-orange-50 dark:hover:bg-slate-700 rounded-full transition-colors duration-200 hidden">
                    <Icon icon="heroicons-outline:emoji-happy" className="text-xl" />
                  </button>
                  <button type="button" className="p-3 text-slate-400 hover:text-orange-500 hover:bg-orange-50 dark:hover:bg-slate-700 rounded-full transition-colors duration-200 hidden">
                    <Icon icon="heroicons-outline:paper-clip" className="text-xl" />
                  </button>
                  <div className="relative flex-grow">
                    <input
                      type="text"
                      className="form-input py-3 px-4 w-full border border-slate-200 dark:border-slate-700 rounded-full focus:ring-2 focus:ring-orange-200 focus:border-orange-500 bg-slate-50 dark:bg-slate-700 dark:text-white transition-all duration-200"
                      placeholder="Type your message here..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                    />
                  </div>
                  <button 
                    type="submit" 
                    className="p-3 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-full hover:shadow-lg hover:from-orange-600 hover:to-orange-700 transition-all duration-200 flex items-center justify-center"
                    disabled={!newMessage.trim()}
                  >
                    <Icon icon="heroicons-outline:paper-airplane" className="transform rotate-90 text-xl" />
                  </button>
                </form>
              </div>
            </div>
          ) : (
            <div className="flex-grow flex items-center justify-center bg-white dark:bg-slate-900">
              <div className="text-center p-8 bg-white dark:bg-slate-800 rounded-2xl shadow-xl max-w-md mx-auto transform transition-all duration-300 hover:scale-105">
                <div className="w-20 h-20 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/30 dark:to-orange-800/30 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Icon icon="heroicons-outline:chat-alt-2" className="text-4xl text-orange-500" />
                </div>
                <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-3">Start Messaging</h3>
                <p className="text-slate-500 dark:text-slate-400 mb-6">
                  Choose an admin from the list to start a conversation. You can search for specific admins using the search box.
                </p>
                <div className="inline-flex items-center text-orange-500 font-medium">
                  <Icon icon="heroicons-outline:arrow-narrow-left" className="mr-2" />
                  Select an admin to begin
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default Chat;







