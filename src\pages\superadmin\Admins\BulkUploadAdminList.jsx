import React, { useEffect, useState } from 'react'
import BulkUsersUpload from '@/components/teachersStudents/BulkUsersUpload';
import { setBreadcrumbs, setPageTitle } from '@/store/common/commonSlice';
import { useDispatch } from 'react-redux';
import { usePostDataMutation } from '@/store/api/apiSlice';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import { LoaderCircle } from 'lucide-react';
import Loading from '@/components/Loading';

const BulkUploadAdminList = () => {
  const dispatch = useDispatch();
  const [loader, setLoader] = useState(false);
  const [postData, { isLoading: isSubmitting }] = usePostDataMutation();
  const navigate = useNavigate();

  useEffect(() => {
    const breadCrumb = [
      { label: "Admins", path: "/all-admins" },
      { label: "Admins Bulk Upload", path: "#" }
    ];
    dispatch(setBreadcrumbs(breadCrumb));
    dispatch(setPageTitle({
      title: "Admins Bulk Upload",
      isBackButton: true
    }));
  }, [dispatch]);

  const [adminKeys, setAdminKeys] = useState(Object.keys({
    "name": "",
    "name_jp": "",
    "email": "",
    "phone": "",
    "role": "",
    "gender": "",
    "password": "",
    "is_active": true
  }));

  const uploadAdminData = async (data) => {
    setLoader(true);
    let payload = {
      "user_type": "admin",
      "admins": data
    }

    try {
      const response = await postData({ url: "/admins/bulk-upload", body: payload }).unwrap().then((res) => {
        if (res?.status === 200) {
          toast.success(res?.data?.message);
          setLoader(false);
          navigate("/all-admins");
        }
      });
    } catch (error) {
      console.log(error?.error?.error);
      setLoader(false);
      toast.error("Failed to upload admin data");
    }
  }

  const columns = [
    {
      header: 'Name',
      accessorKey: 'name',
      cell: info => (
        <span className={info.row.original.duplicate ? 'text-red-500' : ''}>
          {info.getValue()}
        </span>
      ),
    },
    {
      header: 'Email',
      accessorKey: 'email',
      cell: info => (
        <span className={info.row.original.duplicate ? 'text-red-500' : ''}>
          {info.getValue()}
        </span>
      ),
    },
    { header: 'Phone', accessorKey: 'phone' },
    { header: 'Role', accessorKey: 'role' },
    {
      header: 'Duplicate?',
      accessorKey: 'duplicate',
      cell: info =>
        info.getValue() ? (
          <span className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded">Yes</span>
        ) : (
          <span className="text-xs bg-green-100 text-green-600 px-2 py-1 rounded">No</span>
        ),
    },
    {
      header: 'Action',
      accessorKey: 'id',
      // The cell implementation will be provided by BulkUsersUpload component
    },
  ];

  return (
    <div>
      {isSubmitting && <Loading />}
      {!isSubmitting &&  
        <BulkUsersUpload 
          uploadData={uploadAdminData} 
          columns={columns} 
          excelUrl={import.meta.env.VITE_ADMIN_UPLOAD_EXCEL_FILE} 
          dataKeys={adminKeys} 
          compareKeys={["email", "name"]} 
        />
      }
    </div>
  )
}

export default BulkUploadAdminList