// DynamicRenderer.jsx
import React from 'react';
import { useSelector } from 'react-redux';
import { componentRegistry } from '@/constant/componentRegistry';

const DynamicRenderer = () => {
  const { drawerComponentProps, activeComponentKey } = useSelector(state => state.layout);
  if (!activeComponentKey) return null;

  const ComponentToRender = componentRegistry[activeComponentKey];

  return ComponentToRender ? <ComponentToRender {...drawerComponentProps} /> : null;
};

export default DynamicRenderer;
