import React, { useEffect, useState } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import InputField from "@/components/ui/formik-form/InputField";
import InputSelect from "@/components/ui/formik-form/InputSelect";
import Textarea from "@/components/ui/formik-form/Textarea";
import Button from "@/components/ui/Button";
import { handleCustomizer } from "@/store/layout";
import { useDispatch } from "react-redux";
import { 
  usePostDataMutation, 
  useFetchDataQuery 
} from "@/store/api/apiSlice";
import { toast } from "react-toastify";
import { useSelector } from "react-redux";
import { Icon } from "@iconify/react";

const validationSchema = Yup.object().shape({
  name: Yup.string().required("Name is required"),
  email: Yup.string()
    .email("Invalid email format")
    .required("Email is required"),
  age: Yup.number()
    .nullable()
    .transform((value) => (isNaN(value) ? null : value))
    .min(1, "Age must be at least 1"),
  gender: Yup.string()
    .oneOf(["male", "female", "other"], "Please select a valid gender"),
  password: Yup.string()
    .min(6, "Password must be at least 6 characters"),
});



const SingleStudentCreateForm = ({ studentData, isUpdateMode = false, onSuccess }) => {
  const dispatch = useDispatch();
  const [postData, { isLoading: isCreating }] = usePostDataMutation();
  const [uploadImage, setUploadImage] = useState(null);
  const [previewImage, setPreviewImage] = useState(null);
  const { data: countriesData } = useFetchDataQuery("countries");
  const [countryOptions, setCountryOptions] = useState([]);
  const [gradeOptions, setGradeOptions] = useState([]);
  const [schoolOptions, setSchoolOptions] = useState([]);
  const { data: gradesData } = useFetchDataQuery("grades");
  const { data: schoolsData } = useFetchDataQuery("schools");
  
  useEffect(() => {
    if (countriesData?.data) {
      const options = countriesData.data.map(country => ({
        value: String(country.id),
        label: country.name
      }));
      setCountryOptions(options);
    }
  }, [countriesData]);

  useEffect(() => {
    if (gradesData?.data) {
      const options = gradesData.data.map(grade => ({
        value: String(grade.id),
        label: grade.name
      }));
      setGradeOptions(options);
    }
  }, [gradesData]);

  useEffect(() => {
    if (schoolsData?.data?.data) {
      const options = schoolsData.data.data.map(school => ({
        value: String(school.id),
        label: school.name
      }));
      setSchoolOptions(options);
    }
  }, [schoolsData]);

  useEffect(() => {
    // If we have a profile image in studentData, set it
    if (isUpdateMode && studentData.profile_image) {
      setUploadImage(studentData.profile_image);
      setPreviewImage(studentData.profile_image);
    }
  }, [studentData, isUpdateMode]);

  const handleUploadImage = (file) => {
    // Don't block the UI when setting the upload image
    setUploadImage(file);
    if (file) {
      setPreviewImage(file);
    }
  };

  // Add this function to handle form submission
  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      const formData = new FormData();
      
      // Append all form values to formData
      Object.keys(values).forEach(key => {
        if (key !== 'profile_image') {
          // Convert boolean values to 0/1 for API compatibility
          if (typeof values[key] === 'boolean') {
            formData.append(key, values[key] ? 1 : 0);
          } else if (values[key] !== null && values[key] !== undefined) {
            formData.append(key, values[key]);
          }
        }
      });
      
      // Append profile image if exists
      if (uploadImage instanceof File) {
        formData.append('profile_image', uploadImage);
      }
      
      // Add method parameter for update
      const endpoint = isUpdateMode ? `/users/${studentData.id}` : '/users';
      if (isUpdateMode) {
        formData.append('_method', 'PUT');
      }
      
      const response = await postData({ url: endpoint, body: formData }).unwrap();
        
      if (response?.status === 200) {
        toast.success(isUpdateMode ? 'Student updated successfully' : 'Student created successfully');
        if (onSuccess) onSuccess();
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error(error?.data?.message || 'An error occurred while saving student data');
    } finally {
      setSubmitting(false);
    }
  };

  // Define initial values based on whether we're updating or creating
  const getInitialValues = () => {
    if (isUpdateMode && studentData) {
      // For update mode, use studentData with fallbacks for missing fields
      return {
        name: studentData.name || "",
        name_jp: studentData.name_jp || "",
        username: studentData.username || "",
        profile_image: studentData.profile_image || "",
        phone: studentData.phone || "",
        email: studentData.email || "",
        age: studentData.age || "",
        gender: studentData.gender || "",
        // experience: studentData.experience || "",
        school_id: studentData.school_id || "",
        grade_id: studentData.grade_id || "",
        country_id: studentData.country_id ? String(studentData.country_id) : "",
        hobby: studentData.hobby || "",
        favorite_movie: studentData.favorite_movie || "",
        specialty: studentData.specialty || "",
        user_type: studentData.user_type || "student",
        // rating: studentData.rating || "",
        favorite_added: studentData.favorite_added === 1,
        is_active: studentData.is_active === 1,
        password: "", // Don't prefill password for security reasons
      };
    } else {
      // For create mode, use empty values
      return {
        name: "",
        name_jp: "",
        username: "",
        profile_image: "",
        phone: "",
        email: "",
        age: "",
        gender: "",
        // experience: "",
        school_id: "",
        grade_id: "",
        country_id: "",
        hobby: "",
        favorite_movie: "",
        specialty: "",
        user_type: "student",
        // rating: "",
        favorite_added: false,
        is_active: true,
        password: "",
      };
    }
  };

  return (
    <Formik
      initialValues={getInitialValues()}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
    >
      {({ isSubmitting, values, setFieldValue }) => (
        <Form>
          <div className="mb-3">
            {/* Custom Image Upload Component */}
            <div className="border border-dashed border-gray-300 rounded-lg p-6 relative">
              {previewImage ? (
                <div className="flex justify-center">
                  <div className="relative inline-block">
                    <img 
                      src={typeof previewImage === 'string' ? previewImage : URL.createObjectURL(previewImage)} 
                      alt="Profile" 
                      className="w-20 h-20 rounded-md object-cover"
                    />
                    <button
                      type="button"
                      onClick={() => {
                        setPreviewImage(null);
                        setUploadImage(null);
                        setFieldValue("profile_image", "");
                      }}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 w-6 h-6 flex items-center justify-center"
                    >
                      <Icon icon="heroicons:x-mark" className="text-sm" />
                    </button>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center">
                  <Icon icon="heroicons:photo" className="text-3xl text-gray-400 mb-2" />
                  <div className="text-blue-600 text-sm font-medium mb-1">Upload an Image or drag and drop</div>
                  <div className="text-gray-500 text-xs">PNG, JPG, GIF up to 10MB</div>
                  <input 
                    type="file" 
                    className="hidden" 
                    id="profile_image"
                    accept="image/*"
                    onChange={(e) => {
                      if (e.target.files?.[0]) {
                        const file = e.target.files[0];
                        handleUploadImage(file);
                        setFieldValue("profile_image", file);
                      }
                    }}
                  />
                  <label htmlFor="profile_image" className="cursor-pointer w-full h-full absolute top-0 left-0"></label>
                </div>
              )}
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="mb-3">
              <InputField
                label="Student Name"
                name="name"
                type="text"
                placeholder="Enter student name"
                required={true}
              />
            </div>
            <div className="mb-3">
              <InputField
                label="StudentJapanese Name"
                name="name_jp"
                type="text"
                placeholder="Enter Japanese name"
                
              />
            </div>
            <div className="mb-3">
              <InputField
                label="Username"
                name="username"
                type="text"
                placeholder="Enter username"
              />
            </div>
            <div className="mb-3">
              <InputSelect
                label="School ID"
                name="school_id"
                options={schoolOptions}
                placeholder="Select school"
              />
            </div>
            <div className="mb-3">
              <InputSelect
                label="Student Status"
                name="is_active"
                options={[
                  { value: true, label: "Active" },
                  { value: false, label: "Inactive" },
                ]}
                placeholder="Select status"
              />
            </div>
            <div className="mb-3">
              <InputField
                label="Email Address"
                name="email"
                type="email"
                placeholder="Enter student email"
                required={true}
              />
            </div>
            <div className="mb-3">
              <InputField
                label="Password"
                name="password"
                type="password"
                placeholder="Enter password"
              />
            </div>
            <div className="mb-3">
              <InputField
                label="Contact No."
                name="phone"
                type="text"
                placeholder="Enter Contact No."
              />
            </div>
            <div className="mb-3">
              <InputField
                label="Age"
                name="age"
                type="number"
                placeholder="Enter student age"
              />
            </div>
            <div className="mb-3">
              <InputSelect
                label="Gender"
                name="gender"
                options={[
                  { value: "male", label: "Male" },
                  { value: "female", label: "Female" },
                  { value: "other", label: "Other" },
                ]}
                placeholder="Select gender"
              />
            </div>
          <div className="mb-3">
              <InputSelect
                label="Country"
                name="country_id"
                options={countryOptions}
                placeholder="Select country"
              />
            </div>
            
            <div className="mb-3">
              <InputSelect
                label="Grade"
                name="grade_id"
                options={gradeOptions}
                placeholder="Select grade"
              />
            </div>
                <div className="mb-3">
            <Textarea
              label="Favorite Movie"
              name="favorite_movie"
              placeholder="Enter favorite movies"
            />
          </div>
              <div className="mb-3">
            <Textarea
              label="Student Hobbies"
              name="hobby"
              placeholder="Enter hobbies"
            />
          </div>
            <div className="mb-3">
            <Textarea
              label="Student Specialty"
              name="specialty"
              placeholder="Enter specialty"
            />
          </div>
          
            
          </div>
      
          <div className="flex items-center justify-end w-full">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="mt-4 bg-primary-500 text-white hover:bg-primary-400"
            >
              {isSubmitting ? (isUpdateMode ? "Updating..." : "Creating...") : (isUpdateMode ? "Update Student" : "Create Student")}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default SingleStudentCreateForm;

