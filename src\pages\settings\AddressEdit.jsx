import React, { useEffect, useState } from 'react';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import Button from '@/components/ui/Button';
import InputField from '@/components/ui/formik-form/InputField';
import InputSelect from '@/components/ui/formik-form/InputSelect';
import Textarea from '@/components/ui/formik-form/Textarea';
import { useFetchDataQuery, usePostDataMutation } from '@/store/api/apiSlice';
import useAllDivision from '@/hooks/locationHook/useAllDivision';
import useAllArea from '@/hooks/locationHook/useAllArea';
import useAllDistrict from '@/hooks/locationHook/useAllDistrict';
import useAllUpazila from '@/hooks/locationHook/useAllUpazila';
import { handleCustomizer } from '@/store/layout';
import { useDispatch } from 'react-redux';

const validationSchema = Yup.object().shape({
  presentDivision: Yup.string().required("Division is required"),
  presentAddress: Yup.string().required("Present Address is required"),
  presentDistrict: Yup.string().required("District is required"),
  presentUpazila: Yup.string().required("Upazila is required"),
  presentArea: Yup.string().required("Area is required"),
});

const AddressEdit = () => {
  // Mock data for dropdowns
  const {
    data,
    error,
    isLoading: isFetching,
  } = useFetchDataQuery("/admin/user-information");
  
  const [postData, { isLoading: isSubmitting }] = usePostDataMutation();
  const dispatch = useDispatch();
  const [presentDivisionId, setPresentDivisionId] = useState("");
  const [presentDistrictId, setPresentDistrictId] = useState("");
  const [presentUpazilaId, setPresentUpazilaId] = useState("");
  const { divisionOptions: presentDivisionOptions } = useAllDivision();
  const { districtOptions: presentDistrictOptions } = useAllDistrict(presentDivisionId);
  const { upazilaOptions: presentUpazilaOptions } = useAllUpazila(presentDistrictId);
  const { areaOptions: presentAreaOptions } = useAllArea(presentUpazilaId);

  

  const initialValues = {
    presentDivision: "" || data?.data?.present_division_id,
    presentAddress: "" || data?.data?.present_address,
    presentDistrict: "" || data?.data?.present_district_id,
    presentUpazila: "" || data?.data?.present_upazila_id,
    presentArea: "" || data?.data?.present_area_id,
  };

  const handleCloseDrawer = () => {
      dispatch(handleCustomizer(false));
    };

  const handleSubmit = (values) => {
    const formData = {
      present_division_id: values.presentDivision,
      present_district_id: values.presentDistrict,
      present_upazila_id: values.presentUpazila,
      present_area_id: values.presentArea,
      present_address: values.presentAddress,
    };
    postData({ url: "/admin/address-and-update", body: formData });
    handleCloseDrawer();
  };

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
    >
      {({ values, errors, touched, isSubmitting, setFieldValue }) => {
        useEffect(() => {
          if (values.presentDivision) {
            setPresentDivisionId(values.presentDivision);
          }
        }, [values.presentDivision]);

        useEffect(() => {
          if (values.presentDistrict) {
            setPresentDistrictId(values.presentDistrict);
          }
        }, [values.presentDistrict]);

        useEffect(() => {
          if (values.presentUpazila) {
            setPresentUpazilaId(values.presentUpazila);
          }
        }, [values.presentUpazila]);
        return(
          <Form className="relative">
            <div className="space-y-4">
              <div className="grid col-span-1 md:grid-cols-2 gap-6">
                    <div>
                      <InputSelect
                        label="Division"
                        name="presentDivision"
                        options={presentDivisionOptions}
                        error={touched.presentDivision && errors.presentDivision ? errors.presentDivision : ""}
                      />
                      {errors.presentDivision && touched.presentDivision && (
                        <div className="text-red-500 text-base mt-1">
                          {errors.presentDivision}
                        </div>
                      )}
                    </div>
                    <div>
                      <InputSelect
                        label="District"
                        name="presentDistrict"
                        options={presentDistrictOptions}
                        required
                        error={touched.presentDistrict && errors.presentDistrict ? errors.presentDistrict : ""}
                      />
                      {errors.presentDistrict && touched.presentDistrict && (
                        <div className="text-red-500 text-base mt-1">
                          {errors.presentDistrict}
                        </div>
                      )}
                    </div>
                    <div>
                      <InputSelect
                        label="Upazila"
                        name="presentUpazila"
                        options={presentUpazilaOptions}
                        required
                        error={touched.presentUpazila && errors.presentUpazila ? errors.presentUpazila : ""}
                      />
                      {errors.presentUpazila && touched.presentUpazila && (
                        <div className="text-red-500 text-base mt-1">
                          {errors.presentUpazila}
                        </div>
                      )}
                    </div>
                    <div>
                      <InputSelect
                        label="Area"
                        name="presentArea"
                        options={presentAreaOptions}
                        required
                        error={touched.presentArea && errors.presentArea ? errors.presentArea : ""}
                      />
                      {errors.presentArea && touched.presentArea && (
                        <div className="text-red-500 text-base mt-1">
                          {errors.presentArea}
                        </div>
                      )}
                    </div>
                    <div>
                      <Textarea
                        label="Address"
                        name="presentAddress"
                        required
                      />
                    </div>
              </div>
            </div>
            <div className="sticky bottom-0 right-0 flex justify-end pt-5 gap-3 bg-white">
              <Button 
                onClick={handleCloseDrawer}
                text="Cancel" 
                className="bg-secondary-950 hover:bg-secondary-200" 
                iconPosition="right" 
              />
              <Button 
                type="submit"
                text="Submit" 
                className="bg-primary-900 border border-primary-500  hover:bg-primary-900 text-white transition-all duration-300"
                iconPosition="right" 
              />
            </div>
          </Form>
        )
      }}
    </Formik>
  );
};

export default AddressEdit;