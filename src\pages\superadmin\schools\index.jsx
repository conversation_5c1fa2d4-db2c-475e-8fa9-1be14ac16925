import React, { useEffect, useState } from "react";
import { setBreadcrumbs, setPageTitle } from "@/store/common/commonSlice";
import { useDispatch, useSelector } from "react-redux";
import Button from "@/components/ui/Button";
import { handleCustomizer } from "@/store/layout";
import { useFetchDataQuery, usePostDataMutation } from "@/store/api/apiSlice";
import usePagination from '@/hooks/paginationHook/usePagination';
import { Icon } from "@iconify/react";
import { useNavigate, Link } from "react-router-dom";
import useSingleData from "@/hooks/useSingleData";
import Card from "@/components/ui/Card";
import { toast } from "react-toastify";
import Modal from "@/components/ui/Modal";
import SingleSchoolsForm from "@/components/teachersStudents/SingleSchoolsForm";
import DefaultProfile from "@/assets/DefaultProfile.svg";
import { replaceUploadUrl } from "@/helper/helperFunction";
import Table from "@/components/ui/table/Table";
import CountryList from "@/components/CountryList";




const Schools = () => {
  const dispatch = useDispatch();
  const [jobListUrl, setJobListUrl] = useState("/schools");
  const { fetchById, isLoading: isFetchingSchool } = useSingleData();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCountry, setSelectedCountry] = useState("");
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [selectedSchool, setSelectedSchool] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [postData] = usePostDataMutation();
  
  // Fetch countries data for country name mapping
  const { data: countriesData } = useFetchDataQuery("countries");
  
  const {
    data: apiData,
    isLoading,
    currentPage,
    totalPages,
    handlePageChange,
    error,
    handleItemPerPage,
  } = usePagination(jobListUrl, useFetchDataQuery);

  useEffect(() => {
    dispatch(setPageTitle("School List"));
    dispatch(
      setBreadcrumbs([
        { label: "Dashboard", link: "/" },
        { label: "School List" },
      ])
    );
  }, [dispatch]);

  const upDateSingleSchool = async (id) => {
    const schoolData = await fetchById('/schools', id);
    if (schoolData) {
      setSelectedSchool(schoolData);
      setShowUpdateModal(true);
    }
  };


  const uploadSingleSchool = () => {
    console.log('Add New School clicked'); // Debug log
    setSelectedSchool(null);
    setShowUpdateModal(false); // Ensure update modal is closed
    setShowAddForm(true);
  };

  const uploadBulkSchools = () => {
    navigate("/schools-bulk-upload");
  };

  // Function to get country name by country_id
  const getCountryNameById = (countryId) => {
    if (!countriesData?.data) return "N/A";
    const country = countriesData.data.find(country => country.id == countryId);
    return country ? country.name : "N/A";
  };

  // Filter schools based on search term and country
  const filteredSchools = apiData?.data?.data?.filter(school => {
    const matchesSearch = 
      school.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      school.name_jp?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      school.contact_email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      school.full_address?.toLowerCase().includes(searchTerm.toLowerCase());
    // Only match if selectedCountry is set and matches
    const matchesCountry = selectedCountry && school.country_id == selectedCountry;
    return matchesSearch && matchesCountry;
  }) || [];

  // Define table columns
  const columns = [
    {
      header: "#",
      accessorKey: "index",
      cell: ({ row }) => row.index + 1 + (currentPage - 1) * 10,
    },
    {
      header: "Profile",
      accessorKey: "logo",
      cell: ({ row }) => (
        <div className="h-10 w-10 rounded-full overflow-hidden flex-shrink-0">
          <img
            src={row.original.logo ? replaceUploadUrl(row.original.logo) : DefaultProfile}
            alt={row.original.name || "Logo"}
            className="h-full w-full object-cover"
            onError={e => {
              e.target.onerror = null;
              e.target.src = DefaultProfile;
            }}
          />
        </div>
      ),
    },
    { header: "SCHOOL NAME", accessorKey: "name", cell: ({ row }) => row.original.name || "N/A" },
    { header: "NAME JP", accessorKey: "name_jp", cell: ({ row }) => row.original.name_jp || "N/A" },
    { header: "COUNTRY", accessorKey: "country", cell: ({ row }) => getCountryNameById(row.original.country_id) },
    { header: "CITY", accessorKey: "city", cell: ({ row }) => row.original.city || "N/A" },
    { header: "WARD", accessorKey: "ward", cell: ({ row }) => row.original.ward || "N/A" },
    { header: "PREFECTURE", accessorKey: "prefecture", cell: ({ row }) => row.original.prefecture || "N/A" },
    { header: "ADDRESS", accessorKey: "full_address", cell: ({ row }) => row.original.full_address || "N/A" },
    { header: "POSTAL CODE", accessorKey: "postal_code", cell: ({ row }) => row.original.postal_code || "N/A" },
    { header: "PHONE", accessorKey: "contact_phone", cell: ({ row }) => row.original.contact_phone || "N/A" },
    { header: "EMAIL", accessorKey: "contact_email", cell: ({ row }) => row.original.contact_email || "N/A" },
    { header: "TYPE", accessorKey: "school_type", cell: ({ row }) => row.original.school_type || "N/A" },
    { header: "LEVEL", accessorKey: "school_level", cell: ({ row }) => row.original.school_level || "N/A" },
    { header: "EST. YEAR", accessorKey: "established_year", cell: ({ row }) => row.original.established_year || "N/A" },
    {
      header: "ACTIVE",
      accessorKey: "is_active",
      cell: ({ row }) => (
        row.original.is_active ? (
          <span className="inline-block px-2 py-1 text-xs font-semibold bg-green-100 text-green-700 rounded">Active</span>
        ) : (
          <span className="inline-block px-2 py-1 text-xs font-semibold bg-red-100 text-red-700 rounded">Inactive</span>
        )
      ),
    },
    {
      header: "Actions",
      id: "actions",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <button
            className="w-8 h-8 flex items-center justify-center rounded-md bg-white shadow border border-gray-100 hover:bg-gray-50"
            onClick={() => upDateSingleSchool(row.original.id)}
          >
            <Icon icon="heroicons:pencil-square" className="text-slate-400" />
          </button>
          <button
            className="w-8 h-8 flex items-center justify-center rounded-md bg-white shadow border border-gray-100 hover:bg-gray-50"
            onClick={() => navigate(`/schools/view/${row.original.id}`)}
          >
            <Icon icon="heroicons:eye" className="text-slate-400" />
          </button>
          
        </div>
      ),
    },
  ];

  return (
    <div className="page-content page-min-height p-0">
      <div className="schools-page">
        {/* Custom breadcrumb with reduced top padding */}
        <div className="flex items-center gap-2 pt-2 pb-4 mb-4">
          <Link to="/dashboard" className="text-gray-600 hover:text-primary-500">
            <Icon icon="heroicons-outline:home" className="text-lg" />
          </Link>
          <Icon icon="heroicons-outline:chevron-right" className="text-gray-400 text-sm" />
          <span className="text-orange-500 font-medium">School List</span>
        </div>
        
        {showAddForm ? (
          <Card className="border shadow-sm rounded-md overflow-hidden mb-6">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h4 className="font-medium text-xl text-slate-900">Add New School</h4>
                <button 
                  className="text-gray-500 hover:text-gray-700"
                  onClick={() => setShowAddForm(false)}
                >
                  <Icon icon="heroicons-outline:x" className="h-5 w-5" />
                </button>
              </div>
              <SingleSchoolsForm 
                schoolData={null}
                onSuccess={() => {
                  console.log('Form success callback'); // Debug log
                  setShowAddForm(false);
                  navigate('/all-schools');
                }}
              />
            </div>
          </Card>
        ) : (
          <Card className="border shadow-sm rounded-md overflow-hidden">
            <div className="mb-6">
              <div className="flex flex-wrap items-center justify-between gap-4">
                <h4 className="font-medium text-xl text-slate-900">
                  School List
                </h4>
                <div className="flex items-center gap-3">
                  <div className="w-48">
                    <CountryList
                      selectedCountry={selectedCountry}
                      onCountryChange={setSelectedCountry}
                      label=""
                      placeholder="Select Country"
                      className=""
                    />
                  </div>
                  <div className="relative">
                    <input 
                      type="text" 
                      placeholder="Search..." 
                      className="form-input py-2 pl-10 pr-4 rounded-md border border-gray-200 focus:border-primary-500 focus:ring-0"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                      <Icon icon="heroicons-outline:search" className="text-lg" />
                    </span>
                  </div>
                  <button 
                    className="btn inline-flex justify-center rounded-md btn-sm bg-orange-500 text-white hover:bg-orange-600"
                    onClick={uploadBulkSchools}
                  >
                    <span className="flex items-center">
                      <span>Bulk Upload</span>
                    </span>
                  </button>
                  <button 
                    className="btn inline-flex justify-center rounded-md btn-sm bg-orange-500 text-white hover:bg-orange-600"
                    onClick={uploadSingleSchool}
                  >
                    <span className="flex items-center">
                      <span>Add New School</span>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            {/* Only show table if a country is selected */}
            {selectedCountry ? (
              isLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
                </div>
              ) : (
                <Table
                  data={filteredSchools}
                  columns={columns}
                  currentPage={currentPage}
                  handlePageChange={handlePageChange}
                  paginationData={{
                    currentPage,
                    total: totalPages,
                    perPage: 10,
                  }}
                />
              )
            ) : (
              <div className="flex flex-col items-center justify-center min-h-[60vh] py-16">
                <div className="bg-orange-50 shadow-lg rounded-2xl flex flex-col items-center justify-center max-w-2xl w-full py-20 px-10 border border-orange-100">
                  <span className="mb-4">
                    <Icon icon="heroicons-outline:globe-alt" className="text-orange-400" width={48} height={48} />
                  </span>
                  <h2 className="text-2xl font-bold text-orange-700 mb-2 text-center">
                    Please select a country
                  </h2>
                  <p className="text-gray-600 text-base text-center max-w-md">
                    Use the dropdown above to view the school list for a specific country.
                  </p>
                </div>
              </div>
            )}
          </Card>
        )}
        
        {/* Update School Modal */}
        <Modal
          activeModal={showUpdateModal}
          onClose={() => setShowUpdateModal(false)}
          title="Update School"
          centered
          scrollContent
          className="max-w-3xl"
          themeClass="bg-orange-100 dark:bg-slate-800"
        >
          <div className="p-4">
            <SingleSchoolsForm 
              key={selectedSchool?.id || 'new'}
              schoolData={selectedSchool}
              onSuccess={() => {
                setShowUpdateModal(false);
              }}
            />
          </div>
        </Modal>
      </div>
    </div>
  );
};

export default Schools;
