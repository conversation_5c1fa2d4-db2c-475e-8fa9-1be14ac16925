import React from "react";
import <PERSON>wal from "sweetalert2";
import { usePostDataMutation } from "@/store/api/apiSlice";

export const fireResetPassword = async (userId, postData, onSuccess) => {
  Swal.fire({
    title: "Reset Password",
    html: `
      <input id="swal-input-password" type="password" class="swal2-input" placeholder="Enter new password" autocomplete="new-password" />
    `,
    showCancelButton: true,
    confirmButtonText: "Confirm",
    cancelButtonText: "Cancel",
    preConfirm: () => {
      const password = document.getElementById("swal-input-password").value;
      if (!password) {
        Swal.showValidationMessage("Password is required");
        return false;
      }
      if (password.length < 6) {
        Swal.showValidationMessage("Password must be at least 6 characters");
        return false;
      }
      return password;
    },
  }).then(async (result) => {
    if (result.isConfirmed && result.value) {
      try {
        const response = await postData({
          url: `/users/reset-password`,
          body: { password: result.value, user_id: userId },
        }).unwrap();
        if (response?.status === 200) {
          Swal.fire("Success", response?.message || "Password reset successfully!", "success");
          if (onSuccess) onSuccess();
        } else {
          Swal.fire("Error", response?.message || "Failed to reset password", "error");
        }
      } catch (error) {
        Swal.fire("Error", error?.data?.message || "Failed to reset password", "error");
      }
    }
  });
};

const ResetPassword = ({ userId, onSuccess, onCancel }) => {
  const [postData] = usePostDataMutation();

  React.useEffect(() => {
    if (!userId) return;
    Swal.fire({
      title: "Reset Password",
      html: `
        <input id="swal-input-password" type="password" class="swal2-input" placeholder="Enter new password" autocomplete="new-password" />
      `,
      showCancelButton: true,
      confirmButtonText: "Confirm",
      cancelButtonText: "Cancel",
      preConfirm: () => {
        const password = document.getElementById("swal-input-password").value;
        if (!password) {
          Swal.showValidationMessage("Password is required");
          return false;
        }
        if (password.length < 6) {
          Swal.showValidationMessage("Password must be at least 6 characters");
          return false;
        }
        return password;
      },
    }).then(async (result) => {
      if (result.isConfirmed && result.value) {
        try {
          const response = await postData({
            url: `/users/reset-password`,
            body: { password: result.value, user_id: userId },
          }).unwrap();
          if (response?.status === 200) {
            Swal.fire("Success", response?.message || "Password reset successfully!", "success");
            if (onSuccess) onSuccess();
          } else {
            Swal.fire("Error", response?.message || "Failed to reset password", "error");
          }
        } catch (error) {
          Swal.fire("Error", error?.data?.message || "Failed to reset password", "error");
        }
      } else {
        if (onCancel) onCancel();
      }
    });
    // eslint-disable-next-line
  }, [userId]);

  const resetPassword = (userId) => {
    fireResetPassword(userId, postData, handleReset);
  };

  return null;
};

export default ResetPassword;
