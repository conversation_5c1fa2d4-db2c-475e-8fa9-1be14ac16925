import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import React, { useState } from "react";
import BasicInformationEdit from "./BasicInformationEdit";
import GlobalDrawer from "@/components/partials/globalDrawer";
import { useDispatch } from "react-redux";
import { handleCustomizer } from "@/store/layout";
import { useSelector } from "react-redux";
import InformationCard from "@/components/ui/InformationCard";
import AddressEdit from "./AddressEdit";
import ChangePassword from "./PasswordInformation";
import {
    headTitleClass,
    labelClass,
    valueClass,
  } from "./tutorUtility/tutorUtilityCSS";
import { formatDateToISO } from "@/helper/helperFunction";
const ProfileDetails = ({data}) => {
    const dispatch = useDispatch();
    const {customizer} = useSelector((state) => state.layout);
    const [currentDrawer, setCurrentDrawer] = useState(null);
    const [currentDrawerTitle, setCurrentDrawerTitle] = useState(null);

    const toggleDrawer = (val) => {
        switch(val){
            case "basicInformation":
                setCurrentDrawer(<BasicInformationEdit data={data} />);
                setCurrentDrawerTitle("Edit Basic Information");
                dispatch(handleCustomizer(true));
                break;
            case "address":
                setCurrentDrawer(<AddressEdit />);
                setCurrentDrawerTitle("Edit Address");
                dispatch(handleCustomizer(true));
                break;
            default:
                break;
        }
    };

    return (
        <>
            <Card>
                <InformationCard title="Basic Information" actionButton={<Button text="Edit" icon={`heroicons:pencil-square`} onClick={() => toggleDrawer('basicInformation')}></Button>}>
                    <div className="grid lg:grid-cols-12 grid-cols-1 gap-6 p-5 shadow-lg border border-bottom-0 rounded-md">
                        <div className="lg:col-span-6 col-span-12 md:border-r border-slate-200 dark:border-slate-800 border-dashed ">
                            <div>
                                <h5 className={headTitleClass}>Biography</h5>
                                <p className={valueClass}>
                                    {/* Need Biography */}
                                    {data?.bio || "No Data Found"}
                                </p>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="space-y-4 mt-4">
                                    <div>
                                        <span className={labelClass}>father’s name</span>
                                        <span className={valueClass}>
                                        {data?.fathers_name || "No Data Found"}
                                        </span>
                                    </div>
                                    <div>
                                        <span className={labelClass}>Mother’s Name</span>
                                        <span className={valueClass}>
                                        {data?.mothers_name || "No Data Found"}
                                        </span>
                                    </div>
                                    <div>
                                        <span className={labelClass}>DATE OF BIRTH</span>
                                        <span className={valueClass}>
                                        {data?.date_of_birth
                                            ? formatDateToISO(data?.date_of_birth)
                                            : ""}
                                        </span>
                                    </div>
                                    </div>
                                    <div className="space-y-4 mt-4">
                                    <div>
                                        <span className={labelClass}>Religion</span>
                                        <span className={valueClass}>
                                        {data?.religion || "No Data Found"}
                                        </span>
                                    </div>
                                    <div>
                                        <span className={labelClass}>Blood group</span>
                                        <span className={valueClass}>
                                        {/* O+ */}
                                        {data?.blood_group || "No Data Found"}
                                        </span>
                                    </div>
                                    {/* <div>
                                        <span className="block text-sm text-slate-600 dark:text-slate-300 mb-2 uppercase">
                                        Marital status
                                        </span>
                                        <span className="block text-base font-bold text-slate-900 dark:text-slate-200">
                                        {data?.marital_status || "No Data Found"}
                                        </span>
                                    </div> */}
                                    </div>
                                </div>
                                </div>
                        </div>
                        <div className="lg:col-span-6 col-span-12 space-y-5">
                            <div>
                                <h5 className={headTitleClass}>Contact Details</h5>
                                <div className="space-y-4">
                                    <div>
                                    <span className={labelClass}>PRIMARY PHONE</span>
                                    <span className={valueClass}>
                                        {data?.primary_number || ""}
                                    </span>
                                    </div>
                                    <div>
                                    <span className={labelClass}>ALTERNATE PHONE</span>
                                    <span className={valueClass}>
                                        {data?.alternate_number || ""}
                                    </span>
                                    </div>
                                    <div>
                                    <span className={labelClass}>EMAIL</span>
                                    <span className={valueClass}>{data?.email || ""}</span>
                                    </div>
                                    <div className="hidden">
                                        <span className={labelClass}>tutor id</span>
                                        <span className={valueClass}>
                                            {data?.tutor_code || "00"}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>.
                </InformationCard>
                <div className="mt-6 pt-6 ">
                    <InformationCard title="Address" actionButton={<Button text="Edit" icon={`heroicons:pencil-square`} onClick={() => toggleDrawer('address')}></Button>}> 
                    <div className="lg:col-span-6 col-span-12 space-y-5 md:border-r border-slate-200 dark:border-slate-800 border-dashed">
                        <h5 className={headTitleClass}>Present Address</h5>
                        <div className="space-y-3 grid md:grid-cols-2">
                            <div>
                            <span className={labelClass}>Division</span>
                            <span className={valueClass}>
                                {data?.present_division?.name || ""}
                            </span>
                            </div>
                            <div>
                            <span className={labelClass}>District</span>
                            <span className={valueClass}>
                                {data?.present_district?.name || ""}
                            </span>
                            </div>
                            <div>
                            <span className={labelClass}>Upazila</span>
                            <span className={valueClass}>
                                {data?.present_upazila?.name || ""}
                            </span>
                            </div>
                            <div>
                            <span className={labelClass}>Area</span>
                            <span className={valueClass}>
                                {data?.present_area?.name || ""}
                            </span>
                            </div>
                            <div>
                            <span className={labelClass}>Address</span>
                            <span className={valueClass}>
                                {data?.present_address || ""}
                            </span>
                            </div>
                        </div>
                        </div>
                    </InformationCard>
                </div>
            </Card>
            {customizer && 
                <GlobalDrawer title={currentDrawerTitle}>
                    {currentDrawer !== null && currentDrawer}
                </GlobalDrawer>
            }
        </>
    );
};

export default ProfileDetails;
