import { useFetchDataQuery } from "@/store/api/apiSlice";

const useAllUpazila = (districtId) => {
    const { data: upazilaData, error: upazilaError, isLoading: upazilaIsLoading } = useFetchDataQuery(
        districtId ? `/open/upazila-list-by-id/${districtId}` : null,
        {
            skip: !districtId,
        }
    );

    // Transform the API response into dropdown-friendly format
    const upazilaOptions =
        upazilaData?.data?.map((item) => ({
            value: item.id,
            label: item.name,
        })) || [];

    return { upazilaOptions, upazilaError, upazilaIsLoading };
};

export default useAllUpazila;
