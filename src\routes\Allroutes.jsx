import React, { lazy, Suspense } from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import RoleGuard from "./RoleGuard";

// Home pages & dashboard

const Login = lazy(() => import("../pages/auth/login"));
const ForgotPassword = lazy(() => import("../pages/auth/common/forgot-pass"));
const OtpLayout = lazy(() => import("../pages/auth/common/otp"));
const ResetPasswordLayout = lazy(() =>
  import("../pages/auth/common/passwordUpdated")
);
const Error = lazy(() => import("../pages/404"));

import Layout from "@/layout/Layout";
import AuthLayout from "@/layout/AuthLayout";
import Loading from "@/components/Loading";
import Dashboard from "@/pages/dashboard";
import Messages from "@/pages/messages/index";
import Admins from "@/pages/superadmin/Admins";
const Jobs = lazy(() => import("@/pages/jobs"));
const Schools = lazy(() => import("@/pages/superadmin/schools"));
const Students = lazy(() => import("@/pages/superadmin/students"));
const Teachers = lazy(() => import("@/pages/superadmin/teachers"));
const BulkUsersUpload = lazy(() => import( '@/components/teachersStudents/BulkUsersUpload'));
const BulkUploadStudentList = lazy(() => import( "@/pages/superadmin/students/BulkUploadStudentList"));
const BulkUploadTeacherList = lazy(() => import( "@/pages/superadmin/teachers/BulkUploadTeacherList"));
const BulkUploadSchoolsList = lazy(() => import( "@/pages/superadmin/schools/BulkUploadSchoolsList"));
const BulkUploadAdminList = lazy(() => import("@/pages/superadmin/Admins/BulkUploadAdminList"));

const StudentView = lazy(() => import("@/pages/superadmin/students/view/[id]"));
const TeacherView = lazy(() => import("@/pages/superadmin/teachers/view/[id]"));
const SchoolView = lazy(() => import("@/pages/superadmin/schools/view/[id]"));
const AdminView = lazy(() => import("@/pages/superadmin/Admins/view/[id]"));

const Schedules = lazy(() => import( "@/pages/school/schedules"));
import NoticeBoard from "@/pages/NoticeBoard/NoticeBoard";

function Allroutes() {
  return (
    <Routes>
      <Route path="/" element={<AuthLayout />}>
        <Route path="/" element={<Login />} />
        <Route path="/forgot-password" element={<ForgotPassword />} />
        <Route path="/verify-otp" element={<OtpLayout />} />
        <Route path="/reset-password" element={<ResetPasswordLayout />} />
      </Route>

      <Route path="/" element={<Layout />}>
        <Route path="dashboard" element={<Dashboard />} />
        
        {/* Routes accessible by both superadmin and schooladmin */}
        <Route path="/all-students" element={<Students />} />
        <Route path="/all-teachers" element={<Teachers />} />
        <Route path="/all-schedules" element={<Schedules />} />
        <Route path="/students/view/:id" element={<StudentView />} />
        <Route path="/teachers/view/:id" element={<TeacherView />} />
        <Route path="/schools/view/:id" element={<SchoolView />} />
        <Route path="/admins/view/:id" element={<AdminView />} />
        <Route path="/messages" element={<Messages />} />

        
        {/* Routes accessible only by superadmin */}
        <Route 
          path="/students-bulk-upload" 
          element={
            <RoleGuard 
              component={BulkUploadStudentList} 
              allowedRoles={["superadmin"]} 
            />
          } 
        />
        <Route 
          path="/all-schools" 
          element={
            <RoleGuard 
              component={Schools} 
              allowedRoles={["superadmin"]} 
            />
          } 
        />
        <Route 
          path="/teachers-bulk-upload" 
          element={
            <RoleGuard 
              component={BulkUploadTeacherList} 
              allowedRoles={["superadmin"]} 
            />
          } 
        />
        <Route 
          path="/schools-bulk-upload" 
          element={
            <RoleGuard 
              component={BulkUploadSchoolsList} 
              allowedRoles={["superadmin"]} 
            />
          } 
        />
        <Route
         path="/all-admin"
          element={<Admins />} />

        <Route
         path="/admins-bulk-upload" 
         element={
          <RoleGuard 
          component={BulkUploadAdminList} 
          allowedRoles={['superadmin']}>
          </RoleGuard>
        } />
        
        <Route path="/notice-board" element={<NoticeBoard />} />
        
        <Route path="*" element={<Navigate to="/404" />} />
      </Route>
      <Route
        path="/404"
        element={
          <Suspense fallback={<Loading />}>
            <Error />
          </Suspense>
        }
      />
    </Routes>
  );
}

export default Allroutes;
