import React, { useEffect, useState } from "react";
import { setBreadcrumbs, setPageTitle } from "@/store/common/commonSlice";
import { useDispatch } from "react-redux";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import { Icon } from "@iconify/react";
import { useNavigate, Link } from "react-router-dom";
import { useDeleteDataMutation, useGetAdminListQuery } from "@/store/api/apiSlice";
import { toast } from "react-toastify";
import Modal from "@/components/ui/Modal";
import Table from "@/components/ui/table/Table";
import { replaceUploadUrl } from "@/helper/helperFunction";
import DefaultProfile from "@/assets/DefaultProfile.svg";
import AdminForm from "@/components/superadmin/AdminForm";

const Admins = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [selectedAdmin, setSelectedAdmin] = useState(null);
  const [isUpdateMode, setIsUpdateMode] = useState(false);
  const [deleteData] = useDeleteDataMutation();
  const [showAddForm, setShowAddForm] = useState(false);
  const { data: adminApiData, isLoading, isError } = useGetAdminListQuery();
  const admins = adminApiData?.data || [];

  useEffect(() => {
    dispatch(setPageTitle("Admin List"));
    dispatch(
      setBreadcrumbs([
        { label: "Dashboard", link: "/" },
        { label: "Admin List" },
      ])
    );
  }, [dispatch]);

  const addSingleAdmin = () => {
    setSelectedAdmin(null);
    setIsUpdateMode(false);
    setShowAddForm(true);
  };

  const upDateSingleAdmin = async (id) => {
    const adminData = admins.find(a => a.id === id);
    
    if (adminData) {
      setSelectedAdmin(adminData);
      setIsUpdateMode(true);
      setShowModal(true);
    }
  };

  const deleteAdmin = async (id) => {
    if (window.confirm("Are you sure you want to delete this admin?")) {
      try {
        const response = await deleteData({ url: `/admins/${id}` }).unwrap();
        if (response?.status === 200) {
          toast.success("Admin deleted successfully");
        }
      } catch (error) {
        toast.error(error?.data?.message || "Failed to delete admin");
      }
    }
  };

  const uploadBulkAdmins = () => {
    navigate("/admins-bulk-upload");
  };

  const handleFormSuccess = () => {
    setShowAddForm(false);
    toast.success(isUpdateMode ? "Admin updated successfully" : "Admin added successfully");
  };

  const columns = [
    {
      header: "#",
      accessorKey: "index",
      cell: ({ row }) => row.index + 1 + (currentPage - 1) * 10,
    },
    {
      header: "PROFILE",
      accessorKey: "avatar",
      cell: ({ row }) => (
        <div className="h-10 w-10 rounded-full overflow-hidden flex-shrink-0">
          <img
            src={row.original.avatar ? replaceUploadUrl(row.original.avatar) : DefaultProfile}
            alt={row.original.name || "Profile"}
            className="h-full w-full object-cover"
            onError={e => {
              e.target.onerror = null;
              e.target.src = DefaultProfile;
            }}
          />
        </div>
      ),
    },
    { header: "ADMIN NAME", accessorKey: "name", cell: ({ row }) => row.original.name || "N/A" },
    { header: "EMAIL", accessorKey: "email", cell: ({ row }) => row.original.email || "N/A" },
    { header: "PHONE", accessorKey: "phone", cell: ({ row }) => row.original.phone || "N/A" },
    { header: "ROLE", accessorKey: "role", cell: ({ row }) => row.original.role || "N/A" },
    { header: "GENDER", accessorKey: "gender", cell: ({ row }) => row.original.gender || "N/A" },
    {
      header: "STATUS",
      accessorKey: "is_active",
      cell: ({ row }) => (
        row.original.is_active ? (
          <span className="inline-block px-2 py-1 text-xs font-semibold bg-green-100 text-green-700 rounded">Active</span>
        ) : (
          <span className="inline-block px-2 py-1 text-xs font-semibold bg-red-100 text-red-700 rounded">Inactive</span>
        )
      ),
    },
    {
      header: "Actions",
      id: "actions",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <button
            className="w-8 h-8 flex items-center justify-center rounded-md bg-white shadow border border-gray-100 hover:bg-gray-50"
            onClick={() => upDateSingleAdmin(row.original.id)}
          >
            <Icon icon="heroicons:pencil-square" className="text-slate-400" />
          </button>
          <button
            className="w-8 h-8 flex items-center justify-center rounded-md bg-white shadow border border-gray-100 hover:bg-gray-50"
            // onClick={() => navigate(`/admins/view/${row.original.id}`)}
          >
            <Icon icon="heroicons:eye" className="text-slate-400" />
          </button>
        </div>
      ),
    },
  ];

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  return (
    <div className="page-content page-min-height p-0">
      <div className="admins-page">
        {/* Custom breadcrumb */}
        <div className="flex items-center gap-2 pt-2 pb-4 mb-4">
          <Link to="/dashboard" className="text-gray-600 hover:text-primary-500">
            <Icon icon="heroicons-outline:home" className="text-lg" />
          </Link>
          <Icon icon="heroicons-outline:chevron-right" className="text-gray-400 text-sm" />
          <span className="text-orange-500 font-medium">Admin List</span>
        </div>
        {showAddForm ? (
          <Card className="border shadow-sm rounded-md overflow-hidden mb-6">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h4 className="font-medium text-xl text-slate-900">Add New Admin</h4>
                <button 
                  className="text-gray-500 hover:text-gray-700"
                  onClick={() => setShowAddForm(false)}
                >
                  <Icon icon="heroicons-outline:x" className="h-5 w-5" />
                </button>
              </div>
              <AdminForm 
                adminData={null}
                isUpdateMode={false}
                onSuccess={handleFormSuccess}
              />
            </div>
          </Card>
        ) : (
          <Card className="border shadow-sm rounded-md overflow-hidden">
            <div className="mb-6">
              <div className="flex flex-wrap items-center justify-between gap-4">
                <h4 className="font-medium text-xl text-slate-900">
                  Admin List
                </h4>
                <div className="flex items-center gap-3">
                  <button 
                    className="btn inline-flex justify-center rounded-md btn-sm border border-gray-200 bg-white text-gray-700 hover:bg-gray-50 hidden"
                  >
                    <span className="flex items-center">
                      <Icon icon="heroicons-outline:filter" className="ltr:mr-2 rtl:ml-2 text-lg" />
                      <span>Filter</span>
                    </span>
                  </button>
                  <button 
                    className="btn inline-flex justify-center rounded-md btn-sm border border-gray-200 bg-white text-gray-700 hover:bg-gray-50 hidden"
                  >
                    <span className="flex items-center">
                      <Icon icon="heroicons-outline:sort-ascending" className="ltr:mr-2 rtl:ml-2 text-lg" />
                      <span>Sort</span>
                    </span>
                  </button>
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search..."
                      className="pl-8 pr-4 py-2 border rounded-md"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <Icon icon="heroicons-outline:search" className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                  <Button
                    className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 transition duration-300"
                    onClick={() => uploadBulkAdmins()}
                  >
                    Bulk Upload
                  </Button>
                  <Button
                    className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 transition duration-300"
                    onClick={() => addSingleAdmin()}
                  >
                    Add New Admin
                  </Button>
                </div>
              </div>
            </div>
            <Table
              data={admins}
              columns={columns}
              currentPage={currentPage}
              handlePageChange={handlePageChange}
              paginationData={{
                currentPage,
                total: 1,
                perPage: 10,
              }}
            />
          </Card>
        )}
        {/* Admin Modal Form (for update only) */}
        <Modal
          title={isUpdateMode ? "Update Admin" : "Add New Admin"}
          activeModal={showModal}
          onClose={() => setShowModal(false)}
          centered
          className="max-w-2xl"
        >
          <AdminForm 
            adminData={selectedAdmin}
            isUpdateMode={isUpdateMode}
            onSuccess={handleFormSuccess}
          />
        </Modal>
      </div>
    </div>
  );
};

export default Admins;