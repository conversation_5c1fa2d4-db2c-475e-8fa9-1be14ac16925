import React, { useState } from "react";
import Modal from "@/components/ui/Modal";
import Button from "@/components/ui/Button";
import { Icon } from "@iconify/react";

const DeleteModal = ({ show, onClose, item, itemLabel, onDelete }) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleDelete = async () => {
    if (!item?.id) return;

    setIsLoading(true);
    try {
      await onDelete(item); // Call delete function
      onClose();
    } catch (error) {
      console.error("Delete failed:", error);
    }
    setIsLoading(false);
  };

  return (
    <Modal
      activeModal={show}
      onClose={onClose}
      title={`Delete ${itemLabel}`}
      className="max-w-md"
    >
      <div className="p-4 text-center">
        <p className="text-slate-600 mb-6">
          Are you sure you want to delete <strong>"{item?.title}"</strong>?{" "}
          <br />
          This action is irreversible.
        </p>

        <div className="flex justify-center gap-4">
          <Button
            onClick={onClose}
            className="border border-[#175CD3] bg-white px-6 py-2 text-[#175CD3] text-md rounded-md"
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleDelete}
            className={`bg-[#175CD3] flex items-center gap-2 px-6 py-2 text-white text-md rounded-md ${
              isLoading ? "opacity-50 cursor-not-allowed" : ""
            }`}
            disabled={isLoading}
          >
            <Icon icon="mdi:delete-outline" className="w-5 h-5" />
            {isLoading ? "Deleting..." : "Delete"}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteModal;
