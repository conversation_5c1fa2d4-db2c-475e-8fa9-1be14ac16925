import React from "react";
import { useForgetPasswordMutation } from "@/store/api/auth/authApiSlice";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { Formik, Form } from "formik";
import * as yup from "yup";
import InputField from "@/components/ui/formik-form/InputField";
import Button from "@/components/ui/Button";
import Cookies from "js-cookie";

const schema = yup.object({
  email_or_username: yup
    .string()
    .email("Invalid email")
    .required("Email or Phone number is Required"),
  user_type: yup.string().required("User type is required"),
});

const ForgotEmail = () => {
  const [forgetPassword] = useForgetPasswordMutation();
  const navigate = useNavigate();

  const onSubmit = async (data, { setSubmitting }) => {
    try {
      Cookies.set("email_or_username", data.email_or_username, { expires: 1 });
      Cookies.set("user_type", data.user_type, { expires: 1 });

      const res = await forgetPassword(data);

      if (res?.data?.status === true) {
        toast.success(res?.data?.message || "Verification code sent");
        navigate("/verify-otp");
      } else {
        toast.error(res?.data?.message || "Request failed");
      }
    } catch (error) {
      toast.error(error.message);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Formik
      initialValues={{
        email_or_username: "",
        user_type: "Admin",
      }}
      validationSchema={schema}
      onSubmit={onSubmit}
    >
      {({ errors, touched, isSubmitting, handleChange, values }) => (
        <Form className="space-y-4">
          <input type="hidden" name="user_type" value={values.user_type} />
          <InputField
            name="email_or_username"
            label="Email"
            type="email"
            placeholder="Enter Email"
            error={
              errors.email_or_username && touched.email_or_username
                ? errors.email_or_username
                : ""
            }
            className="h-[56px] rounded-md"
            onChange={handleChange}
            value={values.email_or_username}
          />
          <Button
            type="submit"
            text="Send OTP"
            className="bg-secondary-200 block w-full text-center h-[48px] text-black-600 hover:bg-primary-900 hover:text-white"
            isLoading={isSubmitting}
          />
        </Form>
      )}
    </Formik>
  );
};

export default ForgotEmail;
