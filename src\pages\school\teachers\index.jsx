import React, { useEffect, useState } from "react";
import { setBreadcrumbs, setPageTitle } from "@/store/common/commonSlice";
import { useDispatch, useSelector } from "react-redux";
import TableAndGridViewer from "@/components/ui/TableAndGridViewer";
import Dropdown from "@/components/ui/react-hook-form/Dropdown";
import Button from "@/components/ui/Button";
import GlobalDrawer from "@/components/partials/globalDrawer";
import { handleCustomizer } from "@/store/layout";
import { useFetchDataQuery, usePostDataMutation } from "@/store/api/apiSlice";
import usePagination from '@/hooks/paginationHook/usePagination';
import { Icon } from "@iconify/react";
import { useNavigate } from "react-router-dom";

const Teachers = () => {
  const dispatch = useDispatch();
  const [jobListUrl, setJobListUrl] = useState("admin/applied-list");
  const { customizer } = useSelector((state) => state.layout);
  const [currentDrawerTitle, setCurrentDrawerTitle] = useState(null);
  const navigate = useNavigate();
  const {
    data: apiData,
    isLoading,
    currentPage,
    totalPages,
    handlePageChange,
    error,
    handleReset,
    handleItemPerPage,
  } = usePagination(jobListUrl, useFetchDataQuery);

  useEffect(() => {
    const breadCrumb = [{ label: "Teachers", path: "#" }];
    dispatch(setBreadcrumbs(breadCrumb));
    dispatch(setPageTitle({
        title : "Teachers",
        isBackButton : true
    }));
  }, [dispatch]);

  const tableColumnActionsButtons = (row) => {
    return (
      <div className="flex justify-center items-center gap-2">
        <Button
          variant="outline"
          className="p-2 bg-primary-300 text-dark hover:bg-primary-100"
          onClick={() => {
            navigate(`/students/edit/${row.original.id}`);
          }}
        >
          <Icon icon="akar-icons:edit" className="text-lg text-primary-900" />
        </Button>
        <Button
          variant="outline"
          className="p-2 bg-primary-300 text-dark hover:bg-primary-100"
          onClick={() => {
            navigate(`/students/view/${row.original.id}`);
          }}
        >
          <Icon icon="akar-icons:eye" className="text-lg text-primary-900" />
        </Button>
        <Button
          variant="outline"
          className="p-2 bg-primary-300 text-dark hover:bg-primary-100"
          onClick={() => {
            navigate(`/students/reservation/${row.original.id}`);
          }}
        >
          <Icon icon="akar-icons:calendar" className="text-lg text-primary-900" />
        </Button>
        <Button
          variant="outline"
          className="p-2 bg-primary-300 text-dark hover:bg-primary-100"
          onClick={() => {
            if (window.confirm("Are you sure you want to delete this student?")) {
              dispatch(deleteStudent(row.original.id));
            }
          }}
        >
          <Icon icon="akar-icons:trash-bin" className="text-lg text-red-500" />
        </Button>
      </div>
  )
  }

  const columns = [
    {
      header: "Name",
      accessorKey: "name",
      cell: ({ row }) => (
        <div className="text-dark">{row.original.first_name} {row.original.last_name}</div>
      ),
    },
    {
      header: "Age",
      accessorKey: "age",
      cell: ({ row }) => (
        <div className="text-dark">{row.original.age}</div>
      ),
    },
    {
      header: "Gender",
      accessorKey: "gender",
      cell: ({ row }) => (
        <div className="text-dark">{row.original.gender}</div>
      ),
    },
    {
      header: "Address",
      accessorKey: "address",
      cell: ({ row }) => (
        <div className="text-dark">{row.original.address || "N/A"}</div>
      ),
    },
    {
      header: "Action",
      accessorKey: "action",
      cell: ({ row }) => tableColumnActionsButtons(row),
    },
  ];

  const dummyData = [
    {
      id: 1,
      first_name: "John",
      last_name: "Doe",
      age: 25,
      gender: "Male",
      address: "123 Main Street",
    },
    {
      id: 2,
      first_name: "Jane",
      last_name: "Doe",
      age: 30,
      gender: "Female",
      address: "456 ABC Street",
    },
    {
      id: 3,
      first_name: "Bob",
      last_name: "Smith",
      age: 35,
      gender: "Male",
      address: "789 XYZ Street",
    },
  ];

  const uploadBulkStudents = () => {
    let data = {
        active : true,
        key: "uploadbulkStudnts",
        props: {
            title: "Bulk Upload Student",
        },
    }
    dispatch(handleCustomizer(data));
  };

  const uploadSingleTeacher = () => {
    let data = {
        active : true,
        key: "addSingleStudnts",
        props: {
            title: "Add Teacher",
        },
    }
    dispatch(handleCustomizer(data));
  };

  const actionButtons = (
    <div className="flex items-center md:justify-end gap-3 flex-wrap">
      <Button
        className="bg-primary-950 text-white px-4 py-2 rounded-md bg-primary-500 hover:bg-primary-200 transition duration-300"
        icon="heroicons:arrow-path-ragged"
        // onClick={() => uploadBulkStudents()} 
        onClick={() => navigate("/teachers-bulk-upload")} 
      >
        Bulk Upload Teachers
      </Button>
      <Button
        className="bg-primary-950 text-white px-4 py-2 rounded-md bg-primary-500 hover:bg-primary-200 transition duration-300"
        icon="heroicons:user-add"
        onClick={() => uploadSingleTeacher()}
      >
        Add Teacher
      </Button>
    </div>
  );

  return (
    <>
      <div className="students-page">
        <TableAndGridViewer
          columns={columns}
          // data={apiData?.data?.data || []}
          data={dummyData || []}
          pagination={{
            total: apiData?.data?.last_page || 0,
            perPage: apiData?.data?.per_page || 10,
            currentPage: apiData?.data?.current_page || 1,
            totalRecords: apiData?.data?.total || 0,
            dataTo: apiData?.data?.to || 0,
            dataFrom: apiData?.data?.from || 0,
          }}
          actionButtons={actionButtons}
          gridStyle="second"
          gridColumns={3}
          showSearch={true}
          gridSwitcher={false}
          handlePageChange={handlePageChange}
          handleItemPerPage={handleItemPerPage}
        />
      </div>
    </>
  );
};

export default Teachers;
