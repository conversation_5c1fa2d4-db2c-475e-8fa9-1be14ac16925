import React from "react";
import { Navigate } from "react-router-dom";
import Cookies from "js-cookie";

const RoleGuard = ({ component: Component, allowedRoles, ...rest }) => {
  const userInfo = Cookies.get("userInfo");
  const user = userInfo ? JSON.parse(userInfo) : null;
  const userRole = user?.user_type;

  if (!user || !allowedRoles.includes(userRole)) {
    return <Navigate to="/404" />;
  }

  return <Component {...rest} />;
};

export default RoleGuard;

