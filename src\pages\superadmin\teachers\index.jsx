import React, { useEffect, useState } from "react";
import { setBreadcrumbs, setPageTitle } from "@/store/common/commonSlice";
import { useDispatch } from "react-redux";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import { Icon } from "@iconify/react";
import { useNavigate, Link } from "react-router-dom";
import { useDeleteDataMutation, useFetchDataQuery, usePostDataMutation } from "@/store/api/apiSlice";
import { toast } from "react-toastify";
import Modal from "@/components/ui/Modal";
import TeacherForm from "@/components/teachersStudents/TeacherForm";
import Table from "@/components/ui/table/Table";
import { replaceUploadUrl } from "@/helper/helperFunction";
import DefaultProfile from "@/assets/DefaultProfile.svg";
import { fireResetPassword } from "@/components/ResetPassword";

const Teachers = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [selectedTeacher, setSelectedTeacher] = useState(null);
  const [isUpdateMode, setIsUpdateMode] = useState(false);
  const [deleteData] = useDeleteDataMutation();
  const [selectedCountry, setSelectedCountry] = useState("");
  const { data: countriesData } = useFetchDataQuery("countries");
  const { data: teachersData, isLoading: isLoadingTeachers } = useFetchDataQuery(
    selectedCountry ? `teacher-by-country/${selectedCountry}` : null,
    { skip: !selectedCountry }
  );
  const [postData] = usePostDataMutation();

  useEffect(() => {
    dispatch(setPageTitle("Teacher List"));
    dispatch(
      setBreadcrumbs([
        { label: "Dashboard", link: "/" },
        { label: "Teacher List" },
      ])
    );
  }, [dispatch]);

  const addSingleTeacher = () => {
    setSelectedTeacher(null);
    setIsUpdateMode(false);
    setShowModal(true);
  };

  const upDateSingleTeacher = async (id) => {
    // Find teacher data from the API response
    const apiTeachers = teachersData?.data?.data || teachersData?.data || [];
    const teacherData = apiTeachers.find(t => t.id === id);
    
    if (teacherData) {
      setSelectedTeacher(teacherData);
      setIsUpdateMode(true);
      setShowModal(true);
    }
  };

  const deleteTeacher = async (id) => {
    if (window.confirm("Are you sure you want to delete this teacher?")) {
      try {
        const response = await deleteData({ url: `/users/${id}` }).unwrap();
        if (response?.status === 200) {
          toast.success("Teacher deleted successfully");
          // Refresh data if needed
        }
      } catch (error) {
        toast.error(error?.data?.message || "Failed to delete teacher");
      }
    }
  };

  const uploadBulkTeachers = () => {
    navigate("/teachers-bulk-upload");
  };

  const handleFormSuccess = () => {
    setShowModal(false);
    // Refresh data if needed
    toast.success(isUpdateMode ? "Teacher updated successfully" : "Teacher added successfully");
  };

  const handleReset = () => {
    toast.success("Password reset successfully!");
    // Optionally refresh data here
  };

  const resetPassword = (teacherId) => {
    fireResetPassword(teacherId, postData, handleReset);
  };

  const tableColumnActionsButtons = (row) => {
    return (
      <div className="flex justify-center items-center gap-2">
        <Button
          variant="outline"
          className="p-3 bg-white shadow border border-gray-100 rounded-md hover:bg-gray-100"
          onClick={() => upDateSingleTeacher(row.original.id)}
        >
          <Icon icon="akar-icons:edit" className="text-slate-400" />
        </Button>
        <Button
          variant="outline"
          className="p-3 bg-white shadow border border-gray-100 rounded-md hover:bg-gray-100"
          onClick={() => {
            navigate(`/teachers/view/${row.original.id}`);
          }}
        >
          <Icon icon="akar-icons:eye" className="text-slate-400" />
        </Button>
        <Button
          variant="outline"
          className="p-3 bg-white shadow border border-gray-100 rounded-md hover:bg-gray-100"
          onClick={() => deleteTeacher(row.original.id)}
        >
          <Icon icon="akar-icons:trash-bin" className="text-slate-400" />
        </Button>
          <button
            className="w-10 h-10 flex items-center justify-center rounded-md bg-white shadow border border-gray-100 hover:bg-gray-50"
            onClick={() => resetPassword(row.original.id)}
          >
            <Icon icon="heroicons:key" className="text-slate-400" />
          </button>
      </div>
    )
  }

  const renderRating = (rating) => {
    const totalStars = 5;
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const emptyStars = totalStars - fullStars - (hasHalfStar ? 1 : 0);
    
    return (
      <div className="flex items-center">
        <span className="mr-2">{rating}/5</span>
        <div className="flex">
          {[...Array(fullStars)].map((_, i) => (
            <Icon key={i} icon="ic:round-star" className="text-yellow-400" />
          ))}
          {hasHalfStar && (
            <Icon icon="ic:round-star-half" className="text-yellow-400" />
          )}
          {[...Array(emptyStars)].map((_, i) => (
            <Icon key={i + fullStars + (hasHalfStar ? 1 : 0)} icon="ic:round-star-outline" className="text-yellow-400" />
          ))}
        </div>
      </div>
    );
  };

  const columns = [
    {
      header: "#",
      accessorKey: "index",
      cell: ({ row }) => row.index + 1 + (currentPage - 1) * 10,
    },
    {
      header: "PROFILE",
      accessorKey: "profile_image",
      cell: ({ row }) => (
        <div className="h-10 w-10 rounded-full overflow-hidden flex-shrink-0">
          <img
            src={row.original.profile_image ? replaceUploadUrl(row.original.profile_image) : DefaultProfile}
            alt={row.original.name || "Profile"}
            className="h-full w-full object-cover"
            onError={e => {
              e.target.onerror = null;
              e.target.src = DefaultProfile;
            }}
          />
        </div>
      ),
    },
    { header: "TEACHER NAME", accessorKey: "name", cell: ({ row }) => row.original.name || "N/A" },
    { header: "JAPANESE NAME", accessorKey: "name_jp", cell: ({ row }) => row.original.name_jp || "N/A" },
    { header: "USERNAME", accessorKey: "username", cell: ({ row }) => row.original.username || "N/A" },
    { header: "EMAIL", accessorKey: "email", cell: ({ row }) => row.original.email || "N/A" },
    { header: "PHONE", accessorKey: "phone", cell: ({ row }) => row.original.phone || "N/A" },
    { header: "AGE", accessorKey: "age", cell: ({ row }) => row.original.age || "N/A" },
    { header: "GENDER", accessorKey: "gender", cell: ({ row }) => row.original.gender || "N/A" },
    { header: "RATING", accessorKey: "rating", cell: ({ row }) => renderRating(parseFloat(row.original.rating) || 0) },
    { header: "FAVORITE MOVIE", accessorKey: "favorite_movie", cell: ({ row }) => row.original.favorite_movie || "N/A" },
    { header: "SPECIALTY", accessorKey: "specialty", cell: ({ row }) => row.original.specialty || "N/A" },
    {
      header: "STATUS",
      accessorKey: "is_active",
      cell: ({ row }) => (
        row.original.is_active ? (
          <span className="inline-block px-2 py-1 text-xs font-semibold bg-green-100 text-green-700 rounded">Active</span>
        ) : (
          <span className="inline-block px-2 py-1 text-xs font-semibold bg-red-100 text-red-700 rounded">Inactive</span>
        )
      ),
    },
    {
      header: "Actions",
      id: "actions",
      cell: ({ row }) => tableColumnActionsButtons(row),
    },
  ];

  const actionButtons = (
    <div className="flex items-center gap-3">
      <Button
        className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 transition duration-300"
        onClick={() => uploadBulkTeachers()}
      >
        Bulk Upload
      </Button>
      <Button
        className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 transition duration-300"
        onClick={() => addSingleTeacher()}
      >
        Add New Teacher
      </Button>
    </div>
  );

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleItemPerPage = (perPage) => {
    console.log("Items per page changed to:", perPage);
  };

  console.log('countriesData?.data', countriesData?.data)
  console.log('table?.data', teachersData)

  return (
    <div className="page-content page-min-height p-0">
      <div className="schools-page">
        {/* Custom breadcrumb with reduced top padding */}
        <div className="flex items-center gap-2 pt-2 pb-4 mb-4">
          <Link to="/dashboard" className="text-gray-600 hover:text-primary-500">
            <Icon icon="heroicons-outline:home" className="text-lg" />
          </Link>
          <Icon icon="heroicons-outline:chevron-right" className="text-gray-400 text-sm" />
          <span className="text-orange-500 font-medium">Teacher List</span>
        </div>
        
        <Card className="border shadow-sm rounded-md overflow-hidden">
          <div className="mb-6">
            <div className="flex flex-wrap items-center justify-between gap-4">
              <h4 className="font-medium text-xl text-slate-900">
                Teacher List
              </h4>
              <div className="flex items-center gap-3">
                <button 
                  className="btn inline-flex justify-center rounded-md btn-sm border border-gray-200 bg-white text-gray-700 hover:bg-gray-50 hidden"
                  onClick={() => console.log("Filter clicked")}
                >
                  <span className="flex items-center">
                    <Icon icon="heroicons-outline:filter" className="ltr:mr-2 rtl:ml-2 text-lg" />
                    <span>Filter</span>
                  </span>
                </button>
                <button 
                  className="btn inline-flex justify-center rounded-md btn-sm border border-gray-200 bg-white text-gray-700 hover:bg-gray-50 hidden"
                  onClick={() => console.log("Sort clicked")}
                >
                  <span className="flex items-center">
                    <Icon icon="heroicons-outline:sort-ascending" className="ltr:mr-2 rtl:ml-2 text-lg" />
                    <span>Sort</span>
                  </span>
                </button>
                <select
                  className="px-4 py-2 border rounded-md bg-white text-gray-700"
                  value={selectedCountry}
                  onChange={(e) => setSelectedCountry(e.target.value)}
                >
                  <option value="">Select Country</option>
                  {countriesData?.data?.map((country, index) => (
                    <option key={index} value={country.id}>{country.name}</option>
                  ))}
                </select>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search..."
                    className="pl-8 pr-4 py-2 border rounded-md"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <Icon icon="heroicons-outline:search" className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                </div>
                <Button
                  className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 transition duration-300"
                  onClick={() => uploadBulkTeachers()}
                >
                  Bulk Upload
                </Button>
                <Button
                  className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 transition duration-300"
                  onClick={() => addSingleTeacher()}
                >
                  Add New Teacher
                </Button>
              </div>
            </div>
          </div>
          
          {selectedCountry ? (
            isLoadingTeachers ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
              </div>
            ) : (
              <Table
                data={teachersData?.data?.data || teachersData?.data || []}
                columns={columns}
                currentPage={currentPage}
                handlePageChange={handlePageChange}
                paginationData={{
                  currentPage,
                  total: Math.ceil(((teachersData?.data?.data || teachersData?.data)?.length || 0) / 10),
                  perPage: 10,
                }}
              />
            )
          ) : (
            <div className="flex flex-col items-center justify-center min-h-[60vh] py-16">
              <div className="bg-orange-50 shadow-lg rounded-2xl flex flex-col items-center justify-center max-w-2xl w-full py-20 px-10 border border-orange-100">
                <span className="mb-4">
                  <Icon icon="heroicons-outline:globe-alt" className="text-orange-400" width={48} height={48} />
                </span>
                <h2 className="text-2xl font-bold text-orange-700 mb-2 text-center">
                  Please select a country
                </h2>
                <p className="text-gray-600 text-base text-center max-w-md">
                  Use the dropdown above to view the teacher list for a specific country.
                </p>
              </div>
            </div>
          )} 
        </Card>
      </div>

      {/* Teacher Modal Form */}
      <Modal
        title={isUpdateMode ? "Update Teacher" : "Add New Teacher"}
        activeModal={showModal}
        onClose={() => setShowModal(false)}
        centered
        className="max-w-2xl"
      >
        <TeacherForm 
          teacherData={selectedTeacher}
          isUpdateMode={isUpdateMode}
          onSuccess={handleFormSuccess}
        />
      </Modal>
    </div>
  );
};

export default Teachers;
