import React, { useRef, useEffect, useState } from "react";
import Sidebar<PERSON>ogo from "./Logo";
import Navmenu from "./Navmenu";
import { menuItems } from "@/constant/data";
import SimpleBar from "simplebar-react";
import useSidebar from "@/hooks/useSidebar";
import useSemiDark from "@/hooks/useSemiDark";
import useSkin from "@/hooks/useSkin";
import { FiMenu, FiChevronLeft } from "react-icons/fi";

const Sidebar = () => {
  const scrollableNodeRef = useRef();
  const [scroll, setScroll] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (scrollableNodeRef.current.scrollTop > 0) {
        setScroll(true);
      } else {
        setScroll(false);
      }
    };
    scrollableNodeRef.current.addEventListener("scroll", handleScroll);
  }, [scrollableNodeRef]);

  const [collapsed, setMenuCollapsed] = useSidebar();
  const [menuHover, setMenuHover] = useState(false);

  // semi dark option
  const [isSemiDark] = useSemiDark();
  // skin
  const [skin] = useSkin();
  return (
    <div className={isSemiDark ? "dark" : ""}>
      <div
        className={`sidebar-wrapper bg-white ${
          collapsed ? "w-[72px] close_sidebar" : "w-[248px]"
        }
      ${menuHover ? "sidebar-hovered" : ""}
      ${
        skin === "bordered"
          ? "border-r border-slate-200 dark:border-slate-700"
          : "shadow-base"
      }
      `}
        onMouseEnter={() => {
          setMenuHover(true);
        }}
        onMouseLeave={() => {
          setMenuHover(false);
        }}
      >
        <SidebarLogo menuHover={menuHover} />
        <div
          className={`h-[60px]  absolute top-[80px] nav-shadow z-[1] w-full transition-all duration-200 pointer-events-none ${
            scroll ? " opacity-100" : " opacity-0"
          }`}
        ></div>

        <SimpleBar
          className="sidebar-menu px-4 h-[calc(100%-80px)]"
          scrollableNodeProps={{ ref: scrollableNodeRef }}
        >
          <Navmenu menus={menuItems} />
        </SimpleBar>
        <div className="flex items-center justify-between p-4">
          <img src="/logo.png" alt="Logo" className="h-8" />
          <button
            onClick={() => setCollapsed(!collapsed)}
            className="focus:outline-none"
            type="button"
          >
            {collapsed ? (
              <FiMenu size={24} style={{ color: "red" }} />
            ) : (
              <FiChevronLeft size={24} style={{ color: "red" }} />
            )}
          </button>
        </div>
        <nav className="mt-4">
          {/* Example menu items */}
          <ul>
            <li className="flex items-center px-4 py-2">
              <span className="mr-2">🏠</span>
              {!collapsed && <span>Dashboard</span>}
            </li>
            <li className="flex items-center px-4 py-2 bg-orange-500 text-white rounded">
              <span className="mr-2">🏫</span>
              {!collapsed && <span>School List</span>}
            </li>
            {/* ...other menu items... */}
          </ul>
        </nav>
      </div>
    </div>
  );
};

export default Sidebar;
