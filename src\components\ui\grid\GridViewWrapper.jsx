import React, { useState } from "react";
import ProfileGridViewCard from "./ProfileGridViewCard";
import { Icon } from "@iconify/react";
import Pagination from "../Pagination";
import ProfileGridSecondViewCard from "./ProfileGridSecondViewCard";
import ProfileGridThirdViewCard from "./ProfileGridThirdViewCard";

const GridViewWrapper = ({
  data,
  dynamicPagination,
  gridStyle,
  gridColumns,
  paginationData,
  handleItemPerPage,
  handlePageChange,
  isStudentPage = false,
  isparentsPage= false,
  singleView,
  chnageStatus,
  tableColumnActionsButtons
}) => {
  
  return (
    <div>
      {gridStyle === "second" ? (
        <>
          <div
            className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-${
              gridColumns ? gridColumns : 4
            } gap-6`}
          >
            {data.length > 0 && (
              data.map((item, index) => (
                <ProfileGridSecondViewCard singleView={singleView} key={index} data={item} chnageStatus={chnageStatus} />
              ))
            )}
          </div>
        </>
      ): gridStyle === "third" ? (
        <>
          <div
            className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-${
              gridColumns ? gridColumns : 4
            } gap-5 -ml-0 -mr-10`}
          >
            {data.length > 0 && data.map((item, index) => (
              <ProfileGridThirdViewCard singleView={singleView} key={index} data={item} />
            ))}
          </div>
        </>
      ) : (
        <>
          <div
            className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-${
              gridColumns ? gridColumns : 4
            } gap-6`}
          >
            {data.length > 0 && (
              data.map((item, index) => (
                <ProfileGridViewCard singleView={singleView} isStudentPage={isStudentPage} isparentsPage={isparentsPage} key={index} data={item} />
              ))
            )}
          </div>
        </>
      )} 
      {data?.length === 0 && <div className="py-4 px-4 w-full text-center bg-gray-200 text-lg">
          No data found!
        </div>}
      {/* Pagination */}
      {dynamicPagination ? (
        <div className="flex items-center justify-between px-4 py-3 bg-white border-t mt-3">
          {/* Total rows count */}
          <div className="flex-1 text-sm text-gray-700">
            {`${paginationData?.dataFrom}-${paginationData?.dataTo} of ${paginationData?.totalRecords}`}
          </div>

          {/* Page buttons */}
          <div className="flex-1 flex justify-center">
            <nav className="relative z-0 inline-flex -space-x-px gap-5 flex-wrap">
              <button
                onClick={() => handlePageChange(paginationData?.currentPage - 1)}
                disabled={paginationData?.currentPage == 1}
                className={`relative inline-flex items-center px-2 py-2 rounded-full border border-gray-300 bg-white text-sm font-medium ${
                  paginationData?.currentPage == 1
                    ? "text-gray-300"
                    : "text-gray-500 hover:bg-gray-50"
                }`}
              >
                <Icon icon="mdi:chevron-left" className="w-5 h-5" />
              </button>

              {/* Page numbers */}
              {Array.from({ length: 3 }, (_, i) => paginationData?.currentPage - 1 + i).map(
                (pageNumber) =>
                  pageNumber > 0 && pageNumber <= paginationData?.total ? (
                    <button
                      key={pageNumber}
                      onClick={() => handlePageChange(pageNumber)}
                      className={`relative inline-flex items-center rounded-full px-4 py-2 border ${
                        paginationData?.currentPage === pageNumber
                          ? "z-10 bg-blue-50 border-blue-500 text-blue-600"
                          : "bg-white border-gray-300 text-gray-500 hover:bg-gray-50"
                      } text-sm font-medium`}
                    >
                      {pageNumber}
                    </button>
                  ) : null
              )}

              <button
                onClick={() => handlePageChange(paginationData?.currentPage + 1)}
                disabled={paginationData?.currentPage >= paginationData?.total}
                className={`relative inline-flex items-center px-2 py-2 rounded-full border border-gray-300 bg-white text-sm font-medium ${
                  paginationData?.currentPage >= paginationData?.total
                    ? "text-gray-300"
                    : "text-gray-500 hover:bg-gray-50"
                }`}
              >
                <Icon icon="mdi:chevron-right" className="w-5 h-5" />
              </button>
            </nav>
          </div>

          {/* Rows per page selector */}
          <div className="flex-1 flex justify-end items-center">
            <span className="text-sm text-gray-700 mr-2">Rows per page:</span>
            <select
              value={paginationData?.itemsPerPage}
              onChange={(e) => handleItemPerPage(Number(e.target.value))}
              className="border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              {[10, 20, 30, 40, 50].map((pageSize) => (
                <option key={pageSize} value={pageSize}>
                  {pageSize}
                </option>
              ))}
            </select>
          </div>
        </div>
      ) : (
        data?.length > 0 && (
          <>
            <Pagination
              totalPages={paginationData?.total}
              currentPage={paginationData?.currentPage}
              onPageChange={handlePageChange}
            />
          </>
        )
      )}
    </div>
  );
};

export default GridViewWrapper;
