// JobsGridViewCardCard.jsx
import React, { useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { setSelectedGridIds } from '@/store/common/commonSlice';
import { useSelector } from 'react-redux';
import Button from '@/components/ui/Button';

const JobsGridViewCardCard = ({ data, onEdit, onDelete }) => {
  const dispatch = useDispatch();
  const { selectedGridIds } = useSelector((state) => state.commonSlice);

  const onclickSingleSelectBox = useCallback((value) => {
    const newSelectedGridIds = [...selectedGridIds];
    if (newSelectedGridIds.includes(value)) {
      newSelectedGridIds.splice(newSelectedGridIds.indexOf(value), 1);
    } else {
      newSelectedGridIds.push(value);
    }
    dispatch(setSelectedGridIds(newSelectedGridIds));
  }, [selectedGridIds, dispatch]);

  return (
    <div
      className={`cursor-pointer bg-white ${data?.selected ? 'border-success-100' : ''} 
      rounded-lg p-5 relative shadow-lg border w-full md:w-[720px] h-[380px]`}
    >
      <div
        className="absolute top-0 left-0 text-black-700 flex items-center justify-between w-full h-1/2"
        style={{ backgroundColor: '#E3F1FB', height: '45px' }}
      >
        <span className="text-m pl-4">Question Details</span>

        <Button
  className="btn inline-flex justify-center items-center border text-white border-blue-500 hover:bg-blue-600 hover:text-white transition-all duration-300 p-2 px-4 py-1 absolute right-4 top-1/1 text-base bg-[rgb(21,67,96)]" // Set the background color here
  onClick={() => onclickSingleSelectBox(data?.name)}
>
  {data?.buttonText}
</Button>
</div>
        {/* <button
          className="text-white px-4 py-2 rounded-md h-7 flex items-center justify-center absolute right-4 top-1/2 transform -translate-y-1/2"
          style={{ backgroundColor: 'rgb(21, 67, 96)' }}
          onClick={() => onclickSingleSelectBox(data?.name)}
        >
          {data?.buttonText}
        </button>
      </div> */}

      <div className="items-center gap-0 justify-start mt-[calc(1/11*100%)]">
        <div className="text-left">
          <p className="font-bold">Question in English:</p>
          <p>{data?.name}</p>
          <p className="font-bold mt-2">Question in Bengali:</p>
          <p>{data?.bengali}</p>
        </div>

        <div className="mt-4">
          <p className="font-bold">Options</p>
          {data?.options?.map((option, index) => {
            const optionLabel = String.fromCharCode(97 + index); 
            return (
              <div key={index} className="flex items-center gap-2 mb-2">
                <span className="font-semibold">{optionLabel}. </span>
                <span>{option}</span>
              </div>
            );
          })}
        </div>
      </div>

      <div className="absolute bottom-4 right-4 flex gap-4">

          
      <Button text="Edit" iconPosition='right' icon={`heroicons:pencil`} className='btn btn inline-flex justify-center items-center false border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white transition-all duration-300 p-2 text-base' onClick={() => onEdit(data)}  > Edit</Button>
  <Button text="Edit"  iconPosition="right" icon="heroicons:trash" className="btn inline-flex justify-center items-center border border-red-500 text-red-600 hover:bg-red-600 hover:text-white transition-all duration-300 p-2 text-base" onClick={() => onDelete(data)}  >Delete</Button>

       



      </div>
    </div>
  );
};

export default JobsGridViewCardCard;
