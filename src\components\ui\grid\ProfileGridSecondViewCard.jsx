import React, { useCallback, useState } from "react";
import { Icon } from "@iconify/react";
import { useDispatch, useSelector } from "react-redux";
import { setSelectedGridIds } from "@/store/common/commonSlice";
import medalImage from "@/assets/images/all-img/gold-medal-1.png";
import profileImage from "@/assets/images/all-img/tutor_image_with_rattings.png";
import ProgressBarCustom from "@/components/ui/ProgressBarCustom";
import Dropdown from "../react-hook-form/Dropdown";
import noImage from "@/assets/images/all-img/no-image.png";
import { replaceUploadUrl } from "@/helper/helperFunction";
import { useNavigate } from "react-router-dom";
import { tutorssinglrUrl } from "@/constant/data";
import Button from "../Button";


const ProfileGridSecondViewCard = ({ data, singleView, isStudentPage, chnageStatus }) => {
  const dispatch = useDispatch();
  const { selectedGridIds } = useSelector((state) => state.commonSlice);
  const [statusData, setStatusData] = useState(data?.is_active);

  const onclickSingleSelectBox = useCallback(
    (value) => {
      const newSelectedGridIds = [...selectedGridIds];
      if (newSelectedGridIds.includes(value)) {
        newSelectedGridIds.splice(newSelectedGridIds.indexOf(value), 1);
      } else {
        newSelectedGridIds.push(value);
      }
      dispatch(setSelectedGridIds(newSelectedGridIds));
    },
    [selectedGridIds, dispatch]
  );

  const getBgClass = (status)  => {
    switch (status) {
      case "active":
        return "bg-success-200";
      case "inactive":
        return "bg-danger-200";
      case "suspended":
        return "bg-warning-200";
      case "pending":
        return "bg-info-200";
      default:
        return "bg-slate-200";
    }
  }

  return (
    <>
      <div
        className={`cursor-pointer bg-white ${
          data?.selected ? " border-success-500" : ""
        } rounded-lg p-6 relative border`}
        onClick={() => singleView(data?.id, false)}
      >
        
        { data?.status && (
          <div className="absolute top-[7px] right-[0px]">
            <span
              className={`${getBgClass(data?.status)} text-base px-5 py-2 rounded rounded-tr-lg capitalize`}
            >
              {data.status}
            </span>
          </div>
        )}

        <div className="flex items-start gap-3">
          <div>
            {data.profile_image == null ? (
              <img
                src={noImage}
                alt={data.name || "Unknown"}
                width={110}
                height={110}
              />
            ) : (
              <img
                src={replaceUploadUrl(data.profile_image)}
                alt={data.name || "Unknown"}
                width={110}
              />
            )}
          </div>
          <div className="w-full">
            <div className="flex items-center justify-start gap-1 mb-2">
              <img src={medalImage} alt="medal" className="w-8 h-8" />
              <h4  title={data?.name} className="text-lg font-medium text-900 text-left whitespace-nowrap overflow-hidden text-ellipsis">
                {data?.name || "No Data Found"}
              </h4>
            </div>
          </div>
        </div>

        <div className="mt-3 space-y-3">
          <div className="grid grid-cols-3 gap-3 text-black-500">
            <span className="flex items-center gap-2">
              <Icon icon="mdi:school-grade" className="w-5 h-5" />
              <span className="text-center">School ID</span>
            </span>
            <span className="text-center">:</span>
            <span>{data?.school_id}</span>
          </div>
          <div className="grid grid-cols-3 gap-3 text-black-500">
            <span className="flex items-center gap-2">
              <Icon icon="mdi:map-marker-outline" className="w-5 h-5" />
              <span>Email</span>
            </span>
            <span className="text-center">:</span>
            <span className="whitespace-nowrap overflow-hidden text-ellipsis" title={data?.email} >{data?.email}</span>
          </div>
        </div>

        <div className="w-full my-3">
          <hr className="border-dashed" />
        </div>
          {data?.status}
        <div>
          <Button
            className="btn bg-primary-900 text-white px-4 py-2 rounded-md hover:bg-primary-700 w-full"
            onClick={() => console.log("Reservation Button Clicked")}
          >
            Reservation
          </Button>

        </div>
      </div>
    </>
  );
};

export default ProfileGridSecondViewCard;
