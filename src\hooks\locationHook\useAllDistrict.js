import { useFetchDataQuery } from "@/store/api/apiSlice";

const useAllDistrict = (divisionId) => {
    const { data: districtData, error: districtError, isLoading: districtIsLoading } = useFetchDataQuery(
        divisionId ? `/open/district-list-by-id/${divisionId}` : null,
        {
            skip: !divisionId
        }
    );

    // Transform the API response into dropdown-friendly format
    const districtOptions =
        districtData?.data?.map((item) => ({
            value: item.id,
            label: item.name,
        })) || [];

    return { districtOptions, districtError, districtIsLoading };
};

export default useAllDistrict;
