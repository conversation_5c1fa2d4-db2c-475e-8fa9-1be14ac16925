
import React, { useEffect } from 'react'
import BulkUsersUpload from '@/components/teachersStudents/BulkUsersUpload';
import { setBreadcrumbs, setPageTitle } from '@/store/common/commonSlice';
import { useDispatch } from 'react-redux';

const BulkUploadAdminList = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    const breadCrumb = [
      { label: "Admins", path: "/all-admins" },
      { label: "Admins Bulk Upload", path: "#" }
    ];
    dispatch(setBreadcrumbs(breadCrumb));
    dispatch(setPageTitle({
      title: "Admins Bulk Upload", 
      isBackButton: true
    }));
  }, [dispatch]);

  return (
    <div>
      <BulkUsersUpload />
    </div>
  )
}

export default BulkUploadAdminList;