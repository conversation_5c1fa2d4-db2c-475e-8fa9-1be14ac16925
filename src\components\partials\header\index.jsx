
import React from "react";
import Icon from "@/components/ui/Icon";
import HorizentalMenu from "./Tools/HorizentalMenu";
import useWidth from "@/hooks/useWidth";
import useSidebar from "@/hooks/useSidebar";
import useNavbarType from "@/hooks/useNavbarType";
import useMenulayout from "@/hooks/useMenulayout";
import useSkin from "@/hooks/useSkin";
import Logo from "./Tools/Logo";
import SearchModal from "./Tools/SearchModal";
import Profile from "./Tools/Profile";
import Language from "./Tools/Language";
import Notification from "./Tools/Notification";
import useRtl from "@/hooks/useRtl";
import useMobileMenu from "@/hooks/useMobileMenu";
import { useSelector } from "react-redux";

const Header = ({ className = "custom-class bg-white" }, ...props) => {
  const { pageTitle } = useSelector((state) => state.commonSlice);
  const [collapsed, setMenuCollapsed] = useSidebar();
  const { width, breakpoints } = useWidth();
  const [navbarType] = useNavbarType();
  const navbarTypeClass = () => {
    switch (navbarType) {
      case "floating":
        return "floating  has-sticky-header";
      case "sticky":
        return "sticky top-0 z-[999]";
      case "static":
        return "static";
      case "hidden":
        return "hidden";
      default:
        return "sticky top-0";
    }
  };
  const [menuType] = useMenulayout();
  const [skin] = useSkin();
  const [isRtl] = useRtl();

  const [mobileMenu, setMobileMenu] = useMobileMenu();

  const handleOpenMobileMenu = () => {
    setMobileMenu(!mobileMenu);
  };

  const borderSwicthClass = () => {
    if (skin === "bordered" && navbarType !== "floating") {
      return "border-b border-slate-200 dark:border-slate-700";
    } else if (skin === "bordered" && navbarType === "floating") {
      return "border border-slate-200 dark:border-slate-700";
    } else {
      return "dark:border-b dark:border-slate-700 dark:border-opacity-60";
    }
  };
  return (
    <header className={className + " bg-white shadow-sm " + navbarTypeClass()}>
      <div
        className={` app-header md:px-6 px-6 dark:bg-slate-800 bg-white
          ${borderSwicthClass()}
              ${
                menuType === "horizontal" && width > breakpoints.xl
                  ? "py-1"
                  : "md:py-2 py-2"
              }
          `}
      >
        <div className="flex justify-between items-center h-full flex-wrap w-full">
          {/* Left side with logo */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            {/* Logo */}
            <div>
              {menuType === "vertical" && (
                <div className="flex items-center md:space-x-4 space-x-2 rtl:space-x-reverse">
                  {width < breakpoints.xl && <Logo />}
                </div>
              )}
              {/* For Horizontal  */}
              {menuType === "horizontal" && (
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <Logo />
                </div>
              )}
              {/*  Horizontal  Main Menu */}
              {menuType === "horizontal" && width >= breakpoints.xl ? (
                <HorizentalMenu />
              ) : null}
            </div>
          </div>

          {/* Middle section with page title */}
          <div className="md:block">
            {width < breakpoints.xl && 
              <div className="">
                <span className="grow text-2xl text-black-900 line-height-2xl capitalize font-semibold">
                  {pageTitle?.title}
                </span>
              </div>
            }
          </div>

          {/* Right side tools */}
          <div className="nav-tools flex items-center lg:space-x-3 space-x-3 rtl:space-x-reverse flex-wrap md:flex-nowrap">
            {/* Language selector */}
            {/* <Language /> */}
            
            {/* Notification bell with counter */}
            <div className="relative">
              <button className="relative p-1 text-slate-500 hover:text-slate-900 dark:text-slate-400 dark:hover:text-white">
                <Icon icon="heroicons-outline:bell" className="text-xl" />
                <span className="absolute -top-1 -right-1 h-5 w-5 bg-green-500 rounded-full flex items-center justify-center text-white text-xs">3</span>
              </button>
            </div>
            
            <SearchModal />
            {width >= breakpoints.md && <Profile />}
            
            {/* Mobile menu handler */}
            {width <= breakpoints.xl && (
              <div
                className="cursor-pointer text-slate-900 dark:text-white text-2xl"
                onClick={handleOpenMobileMenu}
              >
                <Icon icon="heroicons-outline:menu-alt-3" />
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
