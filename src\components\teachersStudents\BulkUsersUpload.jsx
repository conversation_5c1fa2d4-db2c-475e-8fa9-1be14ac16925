import { setBreadcrumbs, setPageTitle } from '@/store/common/commonSlice';
import { Icon } from '@iconify/react';
import React, { useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { useDispatch } from 'react-redux';
import * as XLSX from 'xlsx';
import SimpleTable from '../ui/table/SecondSimpleTable';
import { toast } from 'react-toastify';
import { Link } from 'react-router-dom';

const BulkUsersUpload = ({excelUrl, dataKeys, uploadData, compareKeys, columns}) => {
  const [files, setFiles] = useState([]);
  const dispatch = useDispatch();
  const [data, setData] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  const handleRemove = (rowId) => {
    setData((prevData) => prevData.filter(item => item.id !== rowId));
  };
  
  // Pass handleRemove to the columns if they need it
  const columnsWithHandleRemove = React.useMemo(() => {
    return columns.map(column => {
      if (column.accessorKey === 'id' && column.header === 'Action') {
        return {
          ...column,
          cell: ({ row }) => (
            <button
              type="button"
              onClick={() => handleRemove(row.original.id)}
              className="text-sm font-semibold text-red-500 hover:text-red-700"
            >
              Remove
            </button>
          )
        };
      }
      return column;
    });
  }, [columns]);

  const markDuplicates = (records, desireKeys) => {
    const seen = new Set();
    return records.map((item) => {
      const isDuplicate = desireKeys.some((key) => {
        if (seen.has(item[key])) {
          return true;
        }
        seen.add(item[key]);
        return false;
      });
      return { ...item, duplicate: isDuplicate };
    });
  };

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: (acceptedFiles) => {
      setFiles(acceptedFiles);
      if (acceptedFiles.length > 0) {
        const promises = acceptedFiles.map((file) => new Promise((resolve, reject) => {
          const reader = new FileReader();

          reader.onload = (e) => {
            const content = e.target.result;
            let parsedData = [];
            try {
              if (file.name.endsWith('.csv')) {
                const rows = content.split('\n').filter(Boolean);
                parsedData = rows.slice(1).map((row, index) => {
                  const rowData = row.split(',');
                  return {
                    id: index,
                    ...dataKeys.reduce((acc, key, index) => {
                      acc[key] = rowData[index]?.trim() || '';
                      return acc;
                    }, {}),
                  };
                });
              } else {
                const workbook = XLSX.read(content, { type: 'binary' });
                const worksheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[worksheetName];
                const jsonData = XLSX.utils.sheet_to_json(worksheet);
                parsedData = jsonData.map((item, index) => ({
                  id: index,
                  ...dataKeys.reduce((acc, key) => {
                    acc[key] = item[key] || '';
                    return acc;
                  }, {}),
                }));
              }

              const withDuplicates = markDuplicates(parsedData, compareKeys);
              resolve(withDuplicates);
            } catch (err) {
              toast.error('Error parsing file');
              reject(err);
            }
          };

          if (file.name.endsWith('.csv')) {
            reader.readAsText(file);
          } else {
            reader.readAsBinaryString(file);
          }
        }));

        Promise.all(promises)
          .then((parsedDataList) => {
            const mergedData = parsedDataList.reduce((acc, curr) => [...acc, ...curr], []);
            setData(mergedData);
          })
          .catch((err) => console.error('Error parsing files:', err));
      }
    },
  });

  const handleUploadValid = () => {
    const validData = data.filter(d => !d.duplicate);
    console.log('validData', data)
    uploadData(validData);
    // TODO: send to API
  };

    

  return (
    <>
        <div className="flex item-center justify-end mb-3">
          <Link to={excelUrl} target="_blank" className="px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600 transition">Download Excel Sample</Link>
        </div>

        <div className="relative rounded-lg border-dashed border-4 border-gray-200 p-6 cursor-pointer hover:border-primary-400 transition-colors duration-200 bg-white">
          <div {...getRootProps()} className="flex flex-col items-center justify-center space-y-4">
            <input {...getInputProps()} />
            <Icon icon="mdi:cloud-upload" className="text-primary-500 text-6xl" />
            <div className="text-center">
              <p className="text-xl font-semibold text-primary-700">Upload a file</p>
              <p className="text-gray-500">Drag and drop a CSV or Excel file here, or click to select one</p>
            </div>
          </div>
        </div>

      <div className='mt-5'>
        {data.length > 0 && (
          <>
            <div className="flex justify-end">
              <button
                type="button"
                onClick={handleUploadValid}
                className="px-4 py-2 bg-primary-500 text-white rounded hover:bg-primary-600 transition"
              >
                Upload Valid Students
              </button>
            </div>
            <SimpleTable
              columns={columnsWithHandleRemove}
              data={data}
            />
          </>
        )}
      </div>
    </>
  );
};

export default BulkUsersUpload;
