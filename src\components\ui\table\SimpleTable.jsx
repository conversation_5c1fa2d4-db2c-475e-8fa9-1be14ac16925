import React from "react";
import { Icon } from "@iconify/react";
import DefaultProfile from "@/assets/DefaultProfile.svg";
import { replaceUploadUrl } from "@/helper/helperFunction";
import { useNavigate } from "react-router-dom";

const SimpleTable = ({ 
  data = [], 
  columns = [], 
  currentPage = 1, 
  onEdit, 
  onView, 
  onDelete,
  entityType = "item",
  showLogo = true,
  showAvatar = false,
  ...props 
}) => {
  const navigate = useNavigate();

  return (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse table-auto">
        <thead>
          <tr className="bg-orange-100 border-b border-slate-200">
            {columns.map((column, index) => (
              <th key={index} className="px-2 py-3 text-left font-medium text-slate-800">
                {column.header}
              </th>
            ))}
            <th className="px-2 py-3 text-left font-medium text-slate-800">Actions</th>
          </tr>
        </thead>
        <tbody>
          {data.length > 0 ? (
            data.map((item, index) => (
              <tr key={item.id || index} className="border-b border-slate-200 hover:bg-slate-50">
                {columns.map((column, colIndex) => (
                  <td key={colIndex} className="px-2 py-3 text-slate-700">
                    {column.key === 'index' ? (
                      index + 1 + (currentPage - 1) * 10
                    ) : column.key === 'profile_image' || column.key === 'logo' ? (
                      <div className="h-10 w-10 rounded-full overflow-hidden flex-shrink-0">
                        {item[column.key] ? (
                          <img 
                            src={replaceUploadUrl(item[column.key])} 
                            alt={item.name || 'Profile'}
                            className="h-full w-full object-cover"
                            onError={(e) => {
                              e.target.onerror = null;
                              e.target.src = DefaultProfile;
                            }}
                          />
                        ) : (
                          <img 
                            src={DefaultProfile} 
                            alt={item.name || 'Profile'}
                            className="h-full w-full object-cover"
                          />
                        )}
                      </div>
                    ) : column.key === 'name' ? (
                      <div>
                        <div className="text-sm font-medium text-gray-900">{item.name || 'N/A'}</div>
                        {item.name_jp && <div className="text-xs text-gray-500">{item.name_jp}</div>}
                      </div>
                    ) : column.key === 'is_active' ? (
                      item[column.key] ? (
                        <span className="inline-block px-2 py-1 text-xs font-semibold bg-green-100 text-green-700 rounded">Active</span>
                      ) : (
                        <span className="inline-block px-2 py-1 text-xs font-semibold bg-red-100 text-red-700 rounded">Inactive</span>
                      )
                    ) : column.key === 'favorite_added' ? (
                      item[column.key] ? 'Yes' : 'No'
                    ) : column.render ? (
                      column.render(item[column.key], item)
                    ) : (
                      <span 
                        className={column.truncate ? "max-w-[180px] truncate overflow-hidden whitespace-nowrap block" : ""} 
                        title={item[column.key] || ''}
                      >
                        {item[column.key] || 'N/A'}
                      </span>
                    )}
                  </td>
                ))}
                <td className="px-2 py-3">
                  <div className="flex items-center gap-2">
                    {onEdit && (
                      <button 
                        className="w-8 h-8 flex items-center justify-center rounded-md bg-white shadow border border-gray-100 hover:bg-gray-50"
                        onClick={() => onEdit(item.id)}
                      >
                        <Icon icon="heroicons:pencil-square" className="text-slate-400" />
                      </button>
                    )}
                    {onView && (
                      <button 
                        className="w-8 h-8 flex items-center justify-center rounded-md bg-white shadow border border-gray-100 hover:bg-gray-50"
                        onClick={() => onView(item.id)}
                      >
                        <Icon icon="heroicons:eye" className="text-slate-400" />
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={columns.length + 1} className="text-center py-8">
                <div className="flex flex-col items-center justify-center">
                  <Icon icon="heroicons-outline:search" className="text-5xl text-gray-300 mb-3" />
                  <h5 className="text-lg font-medium text-gray-900 mb-1">No {entityType}s found</h5>
                  <p className="text-sm text-gray-500">Try adjusting your search or filter to find what you're looking for.</p>
                </div>
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};

export default SimpleTable;
