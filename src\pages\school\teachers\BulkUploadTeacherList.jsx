import React, { useEffect } from 'react'
import BulkUsersUpload from '@/components/teachersStudents/BulkUsersUpload';
import { setBreadcrumbs, setPageTitle } from '@/store/common/commonSlice';
import { useDispatch } from 'react-redux';
const BulkUploadTeacherList = () => {
  const dispatch = useDispatch();

  useEffect(() => {
    const breadCrumb = [
      { label: "Teachers", path: "/all-students" },
      { label: "Teachers Bulk Upload", path: "#" }
    ];
    dispatch(setBreadcrumbs(breadCrumb));
    dispatch(setPageTitle({
      title: "Teachers Bulk Upload",
      isBackButton: true
    }));
  }, [dispatch]);

  return (
    <div>
      <BulkUsersUpload />
    </div>
  )
}

export default BulkUploadTeacherList
