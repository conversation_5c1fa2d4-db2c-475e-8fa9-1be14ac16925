import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setBreadcrumbs, setPageTitle } from "@/store/common/commonSlice";
import Card from "@/components/ui/Card";
import { Icon } from "@iconify/react";
import Chart from "react-apexcharts";
import { useFetchDataQuery } from "@/store/api/apiSlice";

const Dashboard = () => {
  const dispatch = useDispatch();
  
  // Use the dashboard endpoint to get all stats
  const { data: dashboardData, isLoading: isDashboardLoading } = useFetchDataQuery("/dashboard");

  // Extract totals from dashboard response
  const totalStudents = dashboardData?.data?.total_students || 0;
  const totalTeachers = dashboardData?.data?.total_teachers || 0;
  const totalSchools = dashboardData?.data?.total_schools || 0;

  useEffect(() => {
    const breadCrumb = [
      {
        label: "Dashboard",
        path: "#",
      },
    ];
    dispatch(setBreadcrumbs(breadCrumb));
    dispatch(setPageTitle({
      title: "Dashboard",
      isBackButton: false
    }));
  }, [dispatch]);

  // Student enrollment chart
  const enrollmentChartOptions = {
    series: [
      {
        name: "Students",
        data: [31, 40, 28, 51, 42, 109, 100, 120],
      },
    ],
    options: {
      chart: {
        height: 350,
        type: "area",
        fontFamily: 'Inter, sans-serif',
        toolbar: {
          show: false,
        },
        dropShadow: {
          enabled: true,
          top: 10,
          left: 0,
          blur: 3,
          opacity: 0.1,
        },
      },
      colors: ["#F1841B"],
      dataLabels: {
        enabled: false,
      },
      stroke: {
        curve: "smooth",
        width: 2,
      },
      xaxis: {
        categories: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug"],
        labels: {
          style: {
            colors: "#64748b",
            fontSize: '12px',
            fontFamily: 'Inter, sans-serif',
          },
        },
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
      },
      yaxis: {
        labels: {
          style: {
            colors: "#64748b",
            fontSize: '12px',
            fontFamily: 'Inter, sans-serif',
          },
        },
      },
      tooltip: {
        theme: "light",
        x: {
          format: 'dd/MM/yy HH:mm',
        },
        style: {
          fontSize: '12px',
          fontFamily: 'Inter, sans-serif',
        },
      },
      grid: {
        borderColor: "#f1f5f9",
        strokeDashArray: 5,
        xaxis: {
          lines: {
            show: true
          }
        },
        yaxis: {
          lines: {
            show: true
          }
        },
      },
      fill: {
        type: "gradient",
        gradient: {
          shadeIntensity: 1,
          opacityFrom: 0.7,
          opacityTo: 0.2,
          stops: [0, 90, 100],
          colorStops: [
            {
              offset: 0,
              color: "#FFA84D",
              opacity: 0.8
            },
            {
              offset: 100,
              color: "#D3750A",
              opacity: 0.2
            }
          ]
        },
      },
      legend: {
        show: false
      },
    },
  };

  // Teacher distribution chart
  const teacherDistributionOptions = {
    series: [44, 55, 13, 43],
    options: {
      chart: {
        type: 'donut',
        fontFamily: 'Inter, sans-serif',
      },
      colors: ['#FFA84D', '#D3750A', '#B56609', '#965707'],
      labels: ['Elementary', 'Middle School', 'High School', 'College'],
      legend: {
        position: 'bottom',
        fontFamily: 'Inter, sans-serif',
        fontSize: '14px',
        markers: {
          width: 12,
          height: 12,
          radius: 12,
        },
      },
      plotOptions: {
        pie: {
          donut: {
            size: '70%',
            labels: {
              show: true,
              name: {
                show: true,
                fontSize: '22px',
                fontFamily: 'Inter, sans-serif',
                offsetY: -10,
              },
              value: {
                show: true,
                fontSize: '16px',
                fontFamily: 'Inter, sans-serif',
                color: '#64748b',
              },
              total: {
                show: true,
                label: 'Total',
                fontSize: '16px',
                fontFamily: 'Inter, sans-serif',
              }
            }
          }
        }
      },
      dataLabels: {
        enabled: false
      },
      responsive: [{
        breakpoint: 480,
        options: {
          chart: {
            height: 300
          },
          legend: {
            position: 'bottom'
          }
        }
      }]
    }
  };

  return (  
    <div className="space-y-8">
      {/* Welcome Section */}
      <Card className="bg-gradient-to-r from-[#FFA84D] to-[#D3750A] text-white shadow-xl overflow-hidden relative">
        <div className="absolute right-0 top-0 opacity-10">
          <Icon icon="heroicons-outline:light-bulb" className="text-[250px] text-white transform translate-x-20 -translate-y-20" />
        </div>
        <div className="p-8 relative z-10">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-3xl text-white font-bold mb-2">Welcome to Manaful Learning App</h2>
              <p className="text-white text-opacity-90 max-w-2xl">
                Empower your educational journey with comprehensive insights, analytics, and tools designed to enhance learning experiences.
              </p>
              <button className="mt-4 bg-white text-[#D3750A] px-6 py-2 rounded-full font-medium hover:bg-opacity-90 transition-all duration-300 shadow-lg">
                Explore Features
              </button>
            </div>
            <div className="hidden lg:flex items-center justify-center bg-white bg-opacity-20 rounded-full p-6">
              <Icon icon="heroicons-outline:academic-cap" className="text-6xl text-white" />
            </div>
          </div>
        </div>
      </Card>

      {/* Stats Cards */}

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 mb-6">
        <Card className="border-none shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden bg-gradient-to-br from-primary-50 to-white dark:from-primary-900/20 dark:to-slate-800">
          <div className="p-6 relative flex flex-col items-center justify-center text-center">
            <div className="absolute -top-6 -right-6 w-32 h-32">
              <div className="w-full h-full bg-primary-100 dark:bg-primary-500/20 rounded-full opacity-70 transform scale-110"></div>
            </div>
            <div className="absolute -bottom-8 -right-8 w-24 h-24">
              <div className="w-full h-full bg-primary-200 dark:bg-primary-600/20 rounded-full opacity-70"></div>
            </div>
            <div className="relative z-10 flex flex-col items-center justify-center text-center w-full">
              <div className="flex items-center justify-center mb-5 w-full">
                <div className="bg-white dark:bg-slate-700 rounded-xl p-3 shadow-lg flex items-center justify-center">
                  <Icon icon="heroicons-outline:users" className="text-2xl text-primary-600 dark:text-primary-400" />
                </div>
              </div>
              <h3 className="text-lg font-medium text-slate-700 dark:text-slate-300 mb-1">Total Students</h3>
              <div className="mt-2 flex flex-col items-center justify-center">
                <span className="text-3xl font-bold text-slate-900 dark:text-white">{totalStudents}</span>
              </div>
              <div className="mt-5 pt-4 border-t border-slate-200/70 dark:border-slate-700/70 w-full">
                <a href="/all-students" className="text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 flex items-center justify-center font-medium transition-all duration-200 hover:pl-1">
                  View details <Icon icon="heroicons-solid:arrow-right" className="ml-1.5 text-sm transition-all duration-200 group-hover:ml-2.5" />
                </a>
              </div>
            </div>
          </div>
        </Card>
        <Card className="border-none shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden bg-gradient-to-br from-success-50 to-white dark:from-success-900/20 dark:to-slate-800">
          <div className="p-6 relative flex flex-col items-center justify-center text-center">
            <div className="absolute -top-6 -right-6 w-32 h-32">
              <div className="w-full h-full bg-success-100 dark:bg-success-500/20 rounded-full opacity-70 transform scale-110"></div>
            </div>
            <div className="absolute -bottom-8 -right-8 w-24 h-24">
              <div className="w-full h-full bg-success-200 dark:bg-success-600/20 rounded-full opacity-70"></div>
            </div>
            <div className="relative z-10 flex flex-col items-center justify-center text-center w-full">
              <div className="flex items-center justify-center mb-5 w-full">
                <div className="bg-white dark:bg-slate-700 rounded-xl p-3 shadow-lg flex items-center justify-center">
                  <Icon icon="heroicons-outline:academic-cap" className="text-2xl text-success-600 dark:text-success-400" />
                </div>
              </div>
              <h3 className="text-lg font-medium text-slate-700 dark:text-slate-300 mb-1">Total Teachers</h3>
              <div className="mt-2 flex flex-col items-center justify-center">
                <span className="text-3xl font-bold text-slate-900 dark:text-white">{totalTeachers}</span>
              </div>
              <div className="mt-5 pt-4 border-t border-slate-200/70 dark:border-slate-700/70 w-full">
                <a href="/all-teachers" className="text-sm text-success-600 hover:text-success-700 dark:text-success-400 dark:hover:text-success-300 flex items-center justify-center font-medium transition-all duration-200 hover:pl-1">
                  View details <Icon icon="heroicons-solid:arrow-right" className="ml-1.5 text-sm transition-all duration-200 group-hover:ml-2.5" />
                </a>
              </div>
            </div>
          </div>
        </Card>
        <Card className="border-none shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden bg-gradient-to-br from-purple-50 to-white dark:from-purple-900/20 dark:to-slate-800">
          <div className="p-6 relative flex flex-col items-center justify-center text-center">
            <div className="absolute -top-6 -right-6 w-32 h-32">
              <div className="w-full h-full bg-purple-100 dark:bg-purple-500/20 rounded-full opacity-70 transform scale-110"></div>
            </div>
            <div className="absolute -bottom-8 -right-8 w-24 h-24">
              <div className="w-full h-full bg-purple-200 dark:bg-purple-600/20 rounded-full opacity-70"></div>
            </div>
            <div className="relative z-10 flex flex-col items-center justify-center text-center w-full">
              <div className="flex items-center justify-center mb-5 w-full">
                <div className="bg-white dark:bg-slate-700 rounded-xl p-3 shadow-lg flex items-center justify-center">
                  <Icon icon="heroicons-outline:office-building" className="text-2xl text-purple-600 dark:text-purple-400" />
                </div>
              </div>
              <h3 className="text-lg font-medium text-slate-700 dark:text-slate-300 mb-1">Total Schools</h3>
              <div className="mt-2 flex flex-col items-center justify-center">
                <span className="text-3xl font-bold text-slate-900 dark:text-white">{totalSchools}</span>
              </div>
              <div className="mt-5 pt-4 border-t border-slate-200/70 dark:border-slate-700/70 w-full">
                <a href="/all-schools" className="text-sm text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 flex items-center justify-center font-medium transition-all duration-200 hover:pl-1">
                  View details <Icon icon="heroicons-solid:arrow-right" className="ml-1.5 text-sm transition-all duration-200 group-hover:ml-2.5" />
                </a>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Charts and Additional Info */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <Card className="col-span-1 lg:col-span-2 border-none shadow-lg">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white flex items-center">
                <Icon icon="heroicons-outline:chart-square-bar" className="mr-2 text-primary-500" />
                Student Enrollment Trend
              </h3>
              <div className="flex items-center space-x-2">
                <select className="form-select text-sm border-slate-200 dark:border-slate-700 dark:bg-slate-800 rounded-md focus:border-primary-500 focus:ring focus:ring-primary-200 focus:ring-opacity-50">
                  <option>This Year</option>
                  <option>Last Year</option>
                  <option>Last 6 Months</option>
                </select>
                <button className="p-1.5 rounded-md text-slate-400 hover:text-slate-600 dark:hover:text-slate-300">
                  <Icon icon="heroicons-outline:dots-vertical" className="text-lg" />
                </button>
              </div>
            </div>
            <Chart 
              options={enrollmentChartOptions.options}
              series={enrollmentChartOptions.series}
              type="area"
              height={350}
            />
          </div>
        </Card>

        <Card className="border-none shadow-lg">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white flex items-center">
                <Icon icon="heroicons-outline:chart-pie" className="mr-2 text-primary-500" />
                Teacher Distribution
              </h3>
              <button className="p-1.5 rounded-md text-slate-400 hover:text-slate-600 dark:hover:text-slate-300">
                <Icon icon="heroicons-outline:dots-vertical" className="text-lg" />
              </button>
            </div>
            <Chart 
              options={teacherDistributionOptions.options}
              series={teacherDistributionOptions.series}
              type="donut"
              height={300}
            />
          </div>
        </Card>
      </div>

      {/* Recent Activities */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-1">
        <Card className="border-none shadow-lg">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white flex items-center">
                <Icon icon="heroicons-outline:clock" className="mr-2 text-primary-500" />
                Recent Activities
              </h3>
              <button className="text-sm text-primary-600 hover:text-primary-700 font-medium">
                View All
              </button>
            </div>
            <div className="space-y-5">
              {[
                { icon: "heroicons-outline:user-add", color: "bg-primary-100 text-primary-600", title: "New student registered", desc: "Emma Thompson joined Advanced Mathematics", time: "2 hours ago" },
                { icon: "heroicons-outline:calendar", color: "bg-info-100 text-info-600", title: "New course launched", desc: "Introduction to Data Science is now available", time: "4 hours ago" },
                { icon: "heroicons-outline:academic-cap", color: "bg-success-100 text-success-600", title: "New teacher onboarded", desc: "Dr. Michael Chen joined as Science instructor", time: "Yesterday" },
                { icon: "heroicons-outline:badge-check", color: "bg-warning-100 text-warning-600", title: "Course completed", desc: "15 students completed Web Development Fundamentals", time: "2 days ago" },
              ].map((item, index) => (
                <div key={index} className="flex items-start p-4 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700/20 transition-colors duration-150">
                  <div className={`rounded-full p-2 mr-4 ${item.color}`}>
                    <Icon icon={item.icon} className="text-lg" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-slate-900 dark:text-white">{item.title}</p>
                      <span className="text-xs text-slate-500 dark:text-slate-400">{item.time}</span>
                    </div>
                    <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">{item.desc}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
