import React, { useEffect, useState } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import InputField from "@/components/ui/formik-form/InputField";
import InputSelect from "@/components/ui/formik-form/InputSelect";
import Textarea from "@/components/ui/formik-form/Textarea";
import Button from "@/components/ui/Button";
import { handleCustomizer } from "@/store/layout";
import { useDispatch } from "react-redux";
import { 
  usePostDataMutation, 
  useFetchDataQuery 
} from "@/store/api/apiSlice";
import { toast } from "react-toastify";
import ProfileImageUploadFormik from "@/components/ui/formik-form/ProfileImageUploadFormik";
import { useSelector } from "react-redux";
import { Icon } from "@iconify/react";
import { replaceUploadUrl } from "@/helper/helperFunction";

const validationSchema = Yup.object().shape({
  name: Yup.string().required("School name is required"),
  contact_email: Yup.string()
    .email("Invalid email format")
    .required("Contact email is required"),
  contact_phone: Yup.string()
    .required("Contact phone is required"),
  school_type: Yup.string()
    .required("School type is required"),
  school_level: Yup.string()
    .required("School level is required"),
  established_year: Yup.number()
    .min(1800, "Year must be after 1800")
    .max(new Date().getFullYear(), "Year cannot be in the future")
    .required("Established year is required"),
});

const SingleSchoolsForm = ({ schoolData, onSuccess }) => {
  const dispatch = useDispatch();
  const [postData, { isLoading: isCreating }] = usePostDataMutation();
  const [uploadImage, setUploadImage] = useState(null);
  const { data: countriesData } = useFetchDataQuery("countries");
  const [countryOptions, setCountryOptions] = useState([]);
  
  // Check if we have schoolData for update mode
  const isEditMode = schoolData && Object.keys(schoolData || {}).length > 0;

  useEffect(() => {
    if (countriesData?.data) {
      const options = countriesData.data.map(country => ({
        value: country.id,
        label: country.name
      }));
      setCountryOptions(options);
    }
  }, [countriesData]);

  useEffect(() => {
    // If we have a logo in schoolData, set it
    if (isEditMode && schoolData.logo) {
      setUploadImage(replaceUploadUrl(schoolData.logo));
    } else if (!isEditMode) {
      setUploadImage(null);
    }
  }, [schoolData, isEditMode]);

  const handleUploadImage = (file) => {
    setUploadImage(file);
  };


  // Define initial values based on whether we're updating or creating
  const getInitialValues = () => {
    if (isEditMode) {
      // For update mode, use schoolData with fallbacks for missing fields
      return {
        name: schoolData.name || "",
        name_jp: schoolData.name_jp || "",
        logo: schoolData.logo || "",
        contact_phone: schoolData.contact_phone || "",
        contact_email: schoolData.contact_email || "",
        city: schoolData.city || "",
        ward: schoolData.ward || "",
        prefecture: schoolData.prefecture || "",
        full_address: schoolData.full_address || "",
        postal_code: schoolData.postal_code || "",
        country_id: schoolData.country_id || 1,
        school_type: schoolData.school_type || "",
        school_level: schoolData.school_level || "",
        established_year: schoolData.established_year || "",
        is_active: schoolData.is_active === true,
      };
    } else {
      // For create mode, use empty values
      return {
        name: "",
        name_jp: "",
        logo: "",
        contact_phone: "",
        contact_email: "",
        city: "",
        ward: "",
        prefecture: "",
        full_address: "",
        postal_code: "",
        country_id: 1,
        school_type: "",
        school_level: "",
        established_year: "",
        is_active: true,
      };
    }
  };

  const handleSubmit = async (values, { setSubmitting }) => {
    const formData = new FormData();
    // Convert is_active to '1' or '0' for backend compatibility
    const formValues = { ...values, is_active: values.is_active ? "1" : "0" };
    Object.keys(formValues).forEach(key => {
      if (key !== 'logo') {
        formData.append(key, formValues[key]);
      }
    });
    
    // Append logo if exists and is a file
    if (uploadImage instanceof File) {
      formData.append('logo', uploadImage);
    } else if (uploadImage && typeof uploadImage === 'string' && !uploadImage.startsWith('http')) {
      // If it's a string path, don't append it as it's already saved
    }

    try {
      let response;
      let url = "/schools";
      if (isEditMode) {
        formData.append('_method', 'PUT');
        url = `/schools/${schoolData.id}`;
      }
      response = await postData({
        url,
        body: formData
      }).unwrap();
      if (response?.status === 200) {
        toast.success(isEditMode ? "School updated successfully" : "School created successfully");
        if (onSuccess) onSuccess();
      }
    } catch (error) {
      console.error("Error:", error);
      toast.error(error?.data?.message || "An error occurred");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Formik
      initialValues={getInitialValues()}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
      enableReinitialize={true}
    >
      {({ isSubmitting, values, setFieldValue }) => (
        <Form>
          
            <div className="school">
              <h4 className="text-lg font-medium text-slate-900 text-left mt-3">
                Create School
              </h4>
            </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          
            <div className="mb-3">
              <InputField
                label="School Name"
                name="name"
                type="text"
                placeholder="Enter school name"
                required={true}
              />
            </div>
            <div className="mb-3">
              <InputField
                label="School Japanese Name"
                name="name_jp"
                type="text"
                placeholder="Enter Japanese name"
              />
            </div>
            <div className="mb-3">
              <InputField
                label="Email Address"
                name="contact_email"
                type="email"
                placeholder="Enter contact email"
                required={true}
              />
            </div>
            <div className="mb-3">
              <InputField
                label="Password"
                name="password"
                type="password"
                placeholder="Enter password"
              />
              </div>
            <div className="mb-3">
              <InputField
                label="Contact No."
                name="contact_phone"
                type="text"
                placeholder="Enter contact phone"
                required={true}
              />
            </div>
            
            <div className="mb-3">
              <InputSelect
                label="School Status"
                name="is_active"
                options={[
                  { value: true, label: "Active" },
                  { value: false, label: "Inactive" },
                ]}
                placeholder="Select status"
              />
            </div>
             <div className="mb-3">
              <InputSelect
                label="Country"
                name="country_id"
                options={countryOptions}
                placeholder="Select country"
                required={true}
              />
            </div>
            <div className="mb-3">
              <InputField
                label="City"
                name="city"
                type="text"
                placeholder="Enter city"
              />
            </div>
            <div className="mb-3">
              <InputField
                label="Ward"
                name="ward"
                type="text"
                placeholder="Enter ward"
              />
            </div>
             <div className="mb-3">
              <InputField
                label="Postal Code"
                name="postal_code"
                type="text"
                placeholder="Enter postal code"
              />
            </div>
            <div className="mb-3">
              <InputSelect
                label="School Type"
                name="school_type"
                options={[
                  { value: "public", label: "Public" },
                  { value: "private", label: "Private" },
                  { value: "international", label: "International" },
                ]}
                placeholder="Select school type"
                required={true}
              />
            </div>
                <div className="mb-3">
              <InputSelect
                label="School Level"
                name="school_level"
                options={[
                  { value: "elementary", label: "Elementary School" },
                  { value: "middle_school", label: "Middle School" },
                  { value: "high_school", label: "High School" },
                  { value: "university", label: "University" },
                ]}
                placeholder="Select school level"
                required={true}
              />
            </div>
               <div className="mb-3">
              <InputField
                label="Established Year"
                name="established_year"
                type="number"
                placeholder="Enter established year"
                required={true}
              />
            </div>
            <div className="mb-3">
              <InputField
                label="Prefecture"
                name="prefecture"
                type="text"
                placeholder="Enter prefecture"
              />
            </div>
           
            <div className="mb-3">
              <Textarea
                label="School Full Address"
                name="full_address"
                placeholder="Enter full address"
              />
            </div>
           
          
            <div className="mb-3">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Upload Photo
              </label>
              <div className="border border-dashed border-gray-300 rounded-lg p-6 relative">
                {uploadImage ? (
                  <div className="flex justify-center">
                    <div className="relative inline-block">
                      <img 
                        src={uploadImage instanceof File ? URL.createObjectURL(uploadImage) : uploadImage} 
                        alt="School Logo" 
                        className="w-20 h-20 rounded-md object-cover"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          setUploadImage(null);
                          setFieldValue("logo", "");
                        }}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 w-6 h-6 flex items-center justify-center"
                      >
                        <Icon icon="heroicons:x-mark" className="text-sm" />
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center">
                    <Icon icon="heroicons:photo" className="text-3xl text-gray-400 mb-2" />
                    <div className="text-blue-600 text-sm font-medium mb-1">Upload an image or drag and drop</div>
                    <div className="text-gray-500 text-xs">PNG, JPG, GIF up to 10MB</div>
                    <input 
                      type="file" 
                      className="hidden" 
                      id="logo"
                      accept="image/*"
                      onChange={(e) => {
                        if (e.target.files?.[0]) {
                          const file = e.target.files[0];
                          handleUploadImage(file);
                          setFieldValue("logo", file);
                        }
                      }}
                    />
                    <button
                      type="button"
                      onClick={() => document.getElementById('logo').click()}
                      className="mt-3 px-4 py-2 bg-blue-50 text-blue-600 rounded-md text-sm font-medium"
                    >
                      Select File
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center justify-end w-full">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="mt-4 bg-primary-500 text-white hover:bg-primary-400"
            >
              {isSubmitting ? (isEditMode ? "Updating..." : "Creating...") : (isEditMode ? "Update School" : "Create School")}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default SingleSchoolsForm;

