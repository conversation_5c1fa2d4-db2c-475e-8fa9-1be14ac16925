import React, { useEffect, useState } from "react";
import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

// Register Chart.js components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

const JobPostChart = ({ data }) => {
  const [chartData, setChartData] = useState(null);
  const [chartOptions, setChartOptions] = useState({});

  useEffect(() => {
    if (!data || !Array.isArray(data)) return;

    const last10 = data.slice(-10);

    const categories = last10.map((entry) => {
      const date = new Date(entry.date);
      const month = date.toLocaleString("default", { month: "short" });
      const day = date.getDate();
      return `${month} ${day}`;
    });

    const seriesData = last10.map((entry) => entry.total_jobs || 0);

    setChartData({
      labels: categories,
      datasets: [
        {
          label: "Job Posts",
          data: seriesData,
          backgroundColor: "#06b6d4",
          borderRadius: 6,
          barThickness: 30,
        },
      ],
    });

    setChartOptions({
      responsive: true,
      plugins: {
        legend: {
          display: true,
          position: "bottom",
        },
        title: {
          display: true,
          text: "Job Posts (Last 10 Days)",
          font: {
            size: 18,
            weight: "bold",
          },
        },
      },
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    });
  }, [data]);

  if (!chartData) return <p>Loading chart...</p>;

  return (
    <div style={{ height: "300px", width: "100%" }}>
      <Bar data={chartData} options={chartOptions} />
    </div>
  );
};

export default JobPostChart;
