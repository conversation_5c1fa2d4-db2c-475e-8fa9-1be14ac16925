export const dashboardUrl = "/dashboard";
export const settingsUrl = "/settings";
export const tutorsUrl = "/tutors";
export const tutorssinglrUrl = "/tutor/single";
export const parentsUrl = "/parents";
export const singleParentsUrl = "/parents/single";
export const singleStudentsUrl = "/student/single";
export const subjectsUrl = "/subjects";
export const classesUrl = "/classes";
export const examsUrl = "/exams";
export const paymentsUrl = "/payments";
export const reportsUrl = "/reports";
export const studentsUrl = "/all-students";
export const teachersUrl = "/all-teachers";
export const schoolsUrl = "/all-schools";
export const schedulesUrl = "/all-schedules";
export const promotionsUrl = "/promotions";
export const allNotifications = "/all-notifications";
export const competencyTestUrl = "/competency";
export const adminlistUrl = "/all-admin";
export const noticeBoardUrl = "/notice-board";
export const messagesUrl = "/messages";

export const menuItems = [
  {
    title: "Dashboard",
    icon: "heroicons-outline:home",
    isOpen: true,
    isHide: true,
    link: dashboardUrl,
  },
  {
    title: "Students List",
    icon: "heroicons-outline:user-group",
    isOpen: true,
    isHide: true,
    link: studentsUrl,
  },
  {
    title: "Schools List",
    icon: "heroicons-outline:user-group",
    isOpen: true,
    isHide: true,
    link: schoolsUrl,
  },
  {
    title: "Teachers List",
    icon: "heroicons-outline:user-group",
    isOpen: true,
    isHide: true,
    link: teachersUrl,
  },
  {
    title: "Admins List",
    icon: "heroicons-outline:user-group",
    isOpen: true,
    isHide: true,
    link: adminlistUrl,
  },
   {
    title: "Academic Calendar",
    icon: "heroicons-outline:calendar",
    isOpen: true,
    isHide: true,
    link: schedulesUrl,
  },
  {
    title: "Notice Board",
    icon: "heroicons-outline:megaphone",
    isOpen: true,
    isHide: true,
    link: noticeBoardUrl,
  },
  {
    title: "Messages",
    icon: "heroicons-outline:chat-alt-2",
    isOpen: true,
    isHide: true,
    link: messagesUrl,
  },



  
  // {
  //   title: "Students",
  //   icon: "heroicons-outline:home",
  //   link: "#",
  //   isHide: true,
  //   child: [
  //     {
  //       childtitle: "All Students",
  //       childlink: "#",
  //     },
  //     {
  //       childtitle: "Create Students",
  //       childlink: "#",
  //     },
  //   ],
  // },
];

export const topMenu = [
];


export const notifications = [
];

export const message = []



export const colors = {
  primary: "#4669FA",
  secondary: "#A0AEC0",
  danger: "#F1595C",
  black: "#111112",
  warning: "#FA916B",
  info: "#0CE7FA",
  light: "#425466",
  success: "#50C793",
  "gray-f7": "#F7F8FC",
  dark: "#1E293B",
  "dark-gray": "#0F172A",
  gray: "#68768A",
  gray2: "#EEF1F9",
  "dark-light": "#CBD5E1",
};

export const hexToRGB = (hex, alpha) => {
  var r = parseInt(hex.slice(1, 3), 16),
    g = parseInt(hex.slice(3, 5), 16),
    b = parseInt(hex.slice(5, 7), 16);

  if (alpha) {
    return "rgba(" + r + ", " + g + ", " + b + ", " + alpha + ")";
  } else {
    return "rgb(" + r + ", " + g + ", " + b + ")";
  }
};



export const toastOption = {
  position: "top-right",
  autoClose: true,
  hideProgressBar: true,
  closeOnClick: false,
  autoClose: 1000,
  draggable: false,
  theme: "light",

};
