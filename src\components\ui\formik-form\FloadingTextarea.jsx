import React from "react";
import { useField } from "formik";

const Textarea = ({ label, required, ...props }) => {
  const [field, meta, helpers] = useField(props);
  const isError = meta.touched && meta.error;
  return (
    <>
      <div className="flex flex-col mb-4">
        <label
          htmlFor={props.id || props.name}
          className="mb-1 text-sm text-gray-700 font-medium"
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <div className="relative w-full border rounded-lg">
          <textarea
            id={props.id || props.name}
            className={`block px-2.5 py-2 w-full text-sm border-0 appearance-none dark:text-white dark:border-gray-600 dark:focus:border-blue-500 focus:outline-none focus:ring-0 focus:border-blue-600 bg-transparent ${
              isError ? "border-red-500" : ""
            }`}
            placeholder={props.placeholder}
            {...field}
            {...props}
            rows={5}
          ></textarea>
        </div>
      </div>
      {isError && <span className="text-red-500 text-xs">{meta.error}</span>}
    </>
  );
};

export default Textarea;
