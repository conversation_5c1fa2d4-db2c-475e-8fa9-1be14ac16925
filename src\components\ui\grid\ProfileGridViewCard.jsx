import React, { useCallback } from "react";
import { Icon } from "@iconify/react";
import { useDispatch, useSelector } from "react-redux";
import { setSelectedGridIds } from "@/store/common/commonSlice";
import { replaceUploadUrl } from "@/helper/helperFunction";
import noImage from "@/assets/images/all-img/no-image.png";
import { tutorssinglrUrl } from "@/constant/data";
import { useNavigate } from "react-router-dom";

const ProfileGridViewCard = ({ data, square = false, isparentsPage = false, isStudentPage = false, hideTitle = false, singleView }) => {
  const dispatch = useDispatch();
  const { selectedGridIds } = useSelector((state) => state.commonSlice);
  
  const onclickSingleSelectBox = useCallback(
    (value) => {
      const newSelectedGridIds = [...selectedGridIds];
      if (newSelectedGridIds.includes(value)) {
        newSelectedGridIds.splice(newSelectedGridIds.indexOf(value), 1);
      } else {
        newSelectedGridIds.push(value);
      }
      dispatch(setSelectedGridIds(newSelectedGridIds));
    },
    [selectedGridIds, dispatch]
  );

  return (
    <div
      className={`cursor-pointer bg-white ${data?.selected ? "border-success-500" : ""} rounded-lg p-6 relative shadow-lg border`}
      // onClick={() => onclickSingleSelectBox(data?.id)}
      onClick={() => singleView(data?.id, false)}
    >
      {isStudentPage && data?.status && (
        <div className="absolute top-[7px] right-[0px]">
          <span
            className={`${
              data?.status === "active"
                ? "bg-success-100 text-success-500"
                : "bg-danger-100 text-danger-500"
            } text-base px-5 py-2 rounded rounded-tr-lg capitalize`}
          >
            {data.status}
          </span>
        </div>
      )}

      <div className="items-center gap-3 justify-start">
        <div className={`w-28 h-28 ${square ? "rounded-sm" : "rounded-md"} flex items-center justify-center overflow-hidden`}>
          {data.profile_image == null ? (
            <img
              src={noImage}
              alt={data.name || "Unknown"}
              width={110}
              height={110}
              className={`${square ? "rounded-sm" : ""} object-cover`}
            />
          ) : (
            <img
              src={replaceUploadUrl(data.profile_image)}
              alt={data.name || "Unknown"}
              width={110}
              height={110}
              className={`${square ? "rounded-sm" : ""} object-cover`}
            />
          )}
        </div>
        <h4 className="text-lg font-medium text-slate-900 text-left mt-3">
         {data.name || "N/A"}
        </h4>
      </div>

      <div className="mt-3 space-y-3">
        {/* {data?.primary_number && ( */}
          <InfoRow
            icon="ic:baseline-phone"
            label="Primary Number"
            value={data.primary_number || "N/A"}
          />
        {/* )} */}

        {data?.jobtittle && (
          <InfoRow
            icon="solar:notebook-linear"
            label="Job Title"
            value={data.jobtittle}
          />
        )}

        {data?.salary && (
          <InfoRow
            icon="solar:notebook-linear"
            label="Salary"
            value={data.salary}
          />
        )}

        {isStudentPage && data?.class && (
          <InfoRow
            icon="solar:notebook-linear"
            label="Class"
            value={data.class || "N/A"}
          />
        )}

        <InfoRow
          icon="solar:letter-linear"
          label="Email"
          value={data.email || "N/A"}
          truncateValue
        />

        {data?.status && (
          <InfoRow
            icon="solar:letter-linear"
            label="Status"
            value={data.status}
          />
        )}

        {isparentsPage && data?.code && (
          <InfoRow
            icon="fa6-solid:code"
            label="Code"
            value={data.code || "N/A"}
          />
        )}

        {data?.date && (
          <InfoRow
            icon="solar:calendar-linear"
            label="Date"
            value={data.date}
          />
        )}

        <InfoRow
          icon="solar:calendar-linear"
          label="REG. Date"
          value={data.reg_date || "N/A"}
        />
      </div>
    </div>
  );
};

const InfoRow = ({ icon, label, value, breakWord = false, truncateValue = false }) => (
  <div className="grid grid-cols-[auto_10px_1fr] md:grid-cols-[200px_10px_1fr] items-start gap-3 text-black w-full">
    <span className="flex items-center gap-2">
      <Icon icon={icon} className="w-5 h-5 shrink-0" />
      {label}
    </span>
    <span className="text-center">:</span>
    <span
      className={`${breakWord ? "break-all" : ""} ${truncateValue ? "whitespace-nowrap overflow-hidden text-ellipsis" : ""}`}
      style={truncateValue ? { maxWidth: "100%", display: "block" } : {}}
      title={truncateValue ? value : ""}
    >
      {value}
    </span>
  </div>
);

export default ProfileGridViewCard;
