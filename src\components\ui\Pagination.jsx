import React from "react";
import { Icon } from "@iconify/react";

const Pagination = ({ currentPage, totalPages, onPageChange }) => {
  // Helper to generate page numbers with ellipsis
  const getPages = () => {
    const pages = [];
    if (totalPages <= 7) {
      for (let i = 1; i <= totalPages; i++) pages.push(i);
    } else {
      if (currentPage <= 4) {
        pages.push(1, 2, 3, 4, 5, '...', totalPages);
      } else if (currentPage >= totalPages - 3) {
        pages.push(1, '...', totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages);
      } else {
        pages.push(1, '...', currentPage - 1, currentPage, currentPage + 1, '...', totalPages);
      }
    }
    return pages;
  };

  const pages = getPages();

  return (
    <div className="flex justify-center items-center w-full py-2">
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className="w-7 h-7 flex items-center justify-center rounded transition-colors text-gray-400 hover:text-orange-500 disabled:opacity-40"
        aria-label="Previous"
      >
        <Icon icon="heroicons-outline:chevron-left" className="w-4 h-4" />
      </button>
      {pages.map((page, idx) =>
        page === '...' ? (
          <span key={idx} className="mx-1 text-gray-400 select-none">...</span>
        ) : (
          <button
            key={page}
            onClick={() => onPageChange(page)}
            className={`w-7 h-7 mx-1 flex items-center justify-center rounded text-sm font-medium transition-colors
              ${currentPage === page ? 'bg-orange-100 text-orange-700 font-semibold' : 'text-gray-700 hover:bg-gray-100'}`}
            style={{ minWidth: 28 }}
            aria-current={currentPage === page ? 'page' : undefined}
          >
            {page}
          </button>
        )
      )}
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className="w-7 h-7 flex items-center justify-center rounded transition-colors text-gray-400 hover:text-orange-500 disabled:opacity-40"
        aria-label="Next"
      >
        <Icon icon="heroicons-outline:chevron-right" className="w-4 h-4" />
      </button>
    </div>
  );
};

export default Pagination;

// import React from 'react'

// const Pagination = ({ currentPage, totalPages, onPageChange, totalItems, itemsPerPage, onItemsPerPageChange }) => {
//   const startItem = (currentPage - 1) * itemsPerPage + 1
//   const endItem = Math.min(currentPage * itemsPerPage, totalItems)

//   return (
//     <div className="flex items-center justify-between">
//       <div className="text-sm text-gray-700">
//         {`${startItem}-${endItem} of ${totalItems}`}
//       </div>

//       <div className="flex items-center gap-2">
//         <button
//           onClick={() => onPageChange(currentPage - 1)}
//           disabled={currentPage === 1}
//           className="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
//         >
//           <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
//             <path d="M15.7049 7.41L14.2949 6L8.29492 12L14.2949 18L15.7049 16.59L11.1249 12L15.7049 7.41Z" fill="#637381"/>
//           </svg>
//         </button>

//         {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
//           <button
//             key={page}
//             onClick={() => onPageChange(page)}
//             className={`w-8 h-8 rounded-full text-sm font-medium ${
//               currentPage === page
//                 ? 'bg-gray-100 text-gray-900'
//                 : 'text-gray-500 hover:bg-gray-50'
//             }`}
//           >
//             {page}
//           </button>
//         ))}

//         <button
//           onClick={() => onPageChange(currentPage + 1)}
//           disabled={currentPage === totalPages}
//           className="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
//         >
//           <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
//             <path d="M8.29492 16.59L9.70492 18L15.7049 12L9.70492 6L8.29492 7.41L12.8749 12L8.29492 16.59Z" fill="#637381"/>
//           </svg>
//         </button>

//         <div className="ml-4 flex items-center">
//           <span className="text-sm text-gray-500 mr-2">Rows per page:</span>
//           <select
//             value={itemsPerPage}
//             onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
//             className="border border-gray-200 rounded py-1 px-2 text-sm text-gray-600 focus:outline-none focus:border-blue-500"
//           >
//             <option value={10}>10</option>
//             <option value={20}>20</option>
//             <option value={30}>30</option>
//             <option value={40}>40</option>
//             <option value={50}>50</option>
//           </select>
//         </div>
//       </div>
//     </div>
//   )
// }

// export default Pagination
