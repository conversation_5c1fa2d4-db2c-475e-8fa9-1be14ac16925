// Admin profile page component
import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, Link } from 'react-router-dom';
import { Icon } from "@iconify/react";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import DefaultProfile from "@/assets/DefaultProfile.svg";
import { replaceUploadUrl } from "@/helper/helperFunction";

const AdminView = () => {
  const [profile, setProfile] = useState({
    name: '',
    email: '',
    role: 'admin',
    lastLogin: '',
    phone: '',
    gender: '',
    is_active: true,
    avatar: ''
  });
  
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const { id } = useParams();

  useEffect(() => {
    // Fetch admin profile data
    const fetchProfile = async () => {
      setLoading(true);
      try {
        // For demo purposes, using mock data
        // In production, uncomment the fetch call
        // const response = await fetch(`/api/admin/profile/${id}`);
        // const data = await response.json();
        
        // Mock data for development
        const mockData = {
          id: parseInt(id),
          name: "Admin User " + id,
          email: `admin${id}@example.com`,
          role: id === "1" ? "Super Admin" : "School Admin",
          lastLogin: "2023-11-30 10:45 AM",
          phone: "+880-123-45678" + id,
          gender: parseInt(id) % 2 === 0 ? "Female" : "Male",
          is_active: true,
          avatar: parseInt(id) % 2 === 0 ? 
            "https://randomuser.me/api/portraits/women/68.jpg" : 
            "https://randomuser.me/api/portraits/men/44.jpg"
        };
        
        setProfile(mockData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching profile:', error);
        setLoading(false);
      }
    };  

    if (id) {
      fetchProfile();
    }
  }, [id]);

  const handleUpdateProfile = async (e) => {
    e.preventDefault();
    try {
      const response = await fetch('/api/admin/profile/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profile)
      });
      
      if (response.ok) {
        alert('Profile updated successfully');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
    }
  };

  return (
    <div className="p-6 bg-[#F5F6F8] min-h-screen">
      {/* Breadcrumbs */}
      <div className="flex items-center text-sm text-gray-400 mb-6">
        <Link to="/" className="text-gray-500 hover:text-orange-500">
          <Icon icon="heroicons-outline:home" className="text-lg" />
        </Link>
        <Icon icon="heroicons-outline:chevron-right" className="mx-2 text-gray-300" />
        <Link to="/all-admin" className="text-gray-500 hover:text-orange-500">
          Admin List
        </Link>
        <Icon icon="heroicons-outline:chevron-right" className="mx-2 text-gray-300" />
        <span className="text-orange-500 font-medium">Admin Profile</span>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
        </div>
      ) : (
        <>
          {/* Profile Header */}
          <div className="bg-[#FFF3E6] rounded-xl p-4 mb-8 shadow flex items-center relative border border-[#FFE0B2]">
            <div className="flex-shrink-0">
              <div className="bg-[#FFD9B3] p-2 rounded-xl w-[136px] h-[150px] flex items-center justify-center">
                {profile.avatar ? (
                  <img
                    src={profile.avatar}
                    alt={profile.name}
                    className="w-[100px] h-[100px] rounded-full object-cover border-2 border-[#FF9800]"
                    onError={(e) => {
                      e.target.src = DefaultProfile;
                    }}
                  />
                ) : (
                  <img
                    src={DefaultProfile}
                    alt={profile.name}
                    className="w-[100px] h-[100px] rounded-full object-cover border-2 border-[#FF9800]"
                  />
                )}
              </div>
            </div>
            <div className="ml-6">
              <h2 className="text-2xl font-bold text-gray-800">{profile.name}</h2>
              <p className="text-gray-600">{profile.role}</p>
              <div className="mt-2 flex items-center">
                <span className={`px-3 py-1 rounded-full text-xs ${profile.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                  {profile.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
              <div className="mt-4 flex space-x-3">
                <Button 
                  className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 transition duration-300"
                  onClick={() => navigate(`/admins/edit/${id}`)}
                >
                  Edit Profile
                </Button>
              </div>
            </div>
          </div>

          {/* Profile Details */}
          <Card className="mb-8">
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-800">Admin Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <p className="text-sm text-gray-500 mb-1">Full Name</p>
                  <p className="font-medium">{profile.name}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-1">Email Address</p>
                  <p className="font-medium">{profile.email}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-1">Phone Number</p>
                  <p className="font-medium">{profile.phone}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-1">Role</p>
                  <p className="font-medium">{profile.role}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-1">Gender</p>
                  <p className="font-medium">{profile.gender}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 mb-1">Last Login</p>
                  <p className="font-medium">{profile.lastLogin}</p>
                </div>
              </div>
            </div>
          </Card>

          {/* Activity Log */}
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-800">Recent Activity</h3>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="bg-orange-100 p-2 rounded-full mr-3">
                    <Icon icon="heroicons-outline:login" className="text-orange-500" />
                  </div>
                  <div>
                    <p className="font-medium">Logged in</p>
                    <p className="text-sm text-gray-500">{profile.lastLogin}</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-blue-100 p-2 rounded-full mr-3">
                    <Icon icon="heroicons-outline:document-text" className="text-blue-500" /> 
                  </div>
                  <div>
                    <p className="font-medium">Updated school settings</p>
                    <p className="text-sm text-gray-500">2023-11-29 15:30 PM</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="bg-green-100 p-2 rounded-full mr-3">
                    <Icon icon="heroicons-outline:user-add" className="text-green-500" />
                  </div>
                  <div>
                    <p className="font-medium">Added new teacher</p>          
                    <p className="text-sm text-gray-500">2023-11-28 09:15 AM</p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </>
      )}
    </div>  
  );
};

export default AdminView;
