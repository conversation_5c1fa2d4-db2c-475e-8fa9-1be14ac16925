import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import { useDispatch } from "react-redux";
import { setBreadcrumbs, setPageTitle } from "@/store/common/commonSlice";
import { Icon } from "@iconify/react";
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import useSingleData from '@/hooks/useSingleData';
import DefaultProfile from "@/assets/DefaultProfile.svg";
import { replaceUploadUrl } from "@/helper/helperFunction";
import Chart from "react-apexcharts";
import { Line } from 'react-chartjs-2';
import Modal from "@/components/ui/Modal";
import SingleStudentCreateForm from "@/components/teachersStudents/SingleStudentCreateForm";
import { useFetchDataQuery } from "@/store/api/apiSlice";
import {
  Chart as ChartJS,
  LineElement,
  PointElement,
  LineController,
  CategoryScale,
  LinearScale,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(
  LineElement,
  PointElement,
  LineController,
  CategoryScale,
  LinearScale,
  Title,
  Tooltip,
  Legend
);

const StudentView = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const { fetchById, isLoading } = useSingleData();
  const [student, setStudent] = useState(null);
  const { data: reservationData, isLoading: isLoadingReservations, error: reservationError } = useFetchDataQuery(
    id ? `/students/reservation-list-by-id/${id}` : null,
    { skip: !id }
  );
  const [activeTab, setActiveTab] = useState(0);
  const [studyPeriod, setStudyPeriod] = useState('Today');
  const [showEditModal, setShowEditModal] = useState(false);

  useEffect(() => {
    dispatch(setPageTitle({
      title: "Student Profile",
      isBackButton: true
    }));
    
    const loadStudent = async () => {
      try {
        const data = await fetchById('/users', id);
        setStudent(data);
      } catch (error) {
        console.error("Error fetching student:", error);
      }
    };
    
    loadStudent();
  }, [id, dispatch, fetchById]);

  if (isLoading || !student) {
    return <div className="flex justify-center items-center h-96">Loading...</div>;
  }

  const tabs = [
    { icon: "heroicons-outline:document-text", label: "Lesson Records" },
    // { icon: "heroicons-outline:clock", label: "Study Hours" },
    // { icon: "heroicons-outline:chart-bar", label: "Course Progress" },
    // { icon: "heroicons-outline:academic-cap", label: "Mock Exam Analysis" }
  ];


  const barChartOptions = {
    chart: {
      type: 'bar',
      height: 350,
      stacked: true,
      toolbar: {
        show: false
      }
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '70%',
      },
    },
    colors: ['#0EA5E9', '#8B5CF6', '#10B981', '#F59E0B'],
    dataLabels: {
      enabled: false
    },
    xaxis: {
      categories: ['10/1/2023', '10/2/2023', '10/3/2023', '10/4/2023', '10/5/2023', '10/6/2023', '10/7/2023', '10/8/2023', '10/9/2023', '10/10/2023', '10/11/2023', '10/12/2023', '10/13/2023'],
      labels: {
        rotate: -45,
        style: {
          fontSize: '10px'
        }
      }
    },
    legend: {
      position: 'bottom'
    },
    fill: {
      opacity: 1
    }
  };

  const barChartSeries = [
    {
      name: 'Math',
      data: [30, 20, 30, 35, 30, 35, 40, 25, 40, 35, 30, 35, 40]
    },
    {
      name: 'English',
      data: [20, 15, 20, 25, 20, 25, 15, 20, 20, 25, 20, 25, 15]
    },
    {
      name: 'Science',
      data: [35, 30, 35, 40, 35, 40, 30, 35, 35, 40, 35, 40, 30]
    },
    {
      name: 'Other',
      data: [15, 10, 15, 20, 15, 20, 15, 10, 15, 20, 15, 20, 15]
    }
  ];

  const data = {
    labels: ['Mock 1', 'Test 2', 'Test 3', 'Test 4', 'Test 5'],
    datasets: [
      {
        label: 'Math',
        data: [12, 20, 7, 22, 15],
        borderColor: '#2563EB',
        backgroundColor: '#2563EB',
        pointBackgroundColor: '#2563EB',
        pointBorderColor: '#2563EB',
        fill: false,
        tension: 0,
      },
      {
        label: 'Science',
        data: [30, 25, 35, 18, 26],
        borderColor: '#F59E0B',
        backgroundColor: '#F59E0B',
        pointBackgroundColor: '#F59E0B',
        pointBorderColor: '#F59E0B',
        fill: false,
        tension: 0,
      },
      {
        label: 'Social',
        data: [2, 13, 12, 15, 14],
        borderColor: '#8B5CF6',
        backgroundColor: '#8B5CF6',
        pointBackgroundColor: '#8B5CF6',
        pointBorderColor: '#8B5CF6',
        fill: false,
        tension: 0,
      },
      {
        label: 'English',
        data: [22, 15, 16, 8, 12],
        borderColor: '#FBBF24',
        backgroundColor: '#FBBF24',
        pointBackgroundColor: '#FBBF24',
        pointBorderColor: '#FBBF24',
        fill: false,
        tension: 0,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        display: true,
        position: 'top',
        align: 'start',
        labels: {
          boxWidth: 12,
          boxHeight: 12,
          usePointStyle: true,
          pointStyle: 'rectRounded',
        },
      },
    },
    elements: {
      line: {
        borderWidth: 2,
      },
      point: {
        radius: 4,
        borderWidth: 2,
      },
    },
    scales: {
      y: {
        title: {
          display: true,
          text: 'Marks',
        },
        beginAtZero: true,
        grid: {
          color: '#F3F4F6',
        },
      },
      x: {
        grid: {
          display: false,
        },
      },
    },
  };

  const handleEditSuccess = async () => {
    setShowEditModal(false);
    // Reload student data after update
    try {
      const data = await fetchById('/users', id);
      setStudent(data);
    } catch (error) {
      // Optionally handle error
    }
  };

  // Add this helper function at the top (after imports)
  function formatFullDateWithOrdinal(dateString) {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    const day = date.getDate();
    const year = date.getFullYear();
    const month = date.toLocaleString('en-US', { month: 'long' });

    // Ordinal suffix
    const getOrdinal = n => {
      const s = ["th", "st", "nd", "rd"],
        v = n % 100;
      return n + (s[(v - 20) % 10] || s[v] || s[0]);
    };

    return `${getOrdinal(day)} ${month} ${year}`;
  }

  // Add this helper function at the top (after imports)
  function formatTimeHM(timeStr) {
    if (!timeStr) return "N/A";
    const [hour, minute] = timeStr.split(":");
    let h = parseInt(hour, 10);
    const m = minute.padStart(2, "0");
    const ampm = h >= 12 ? "pm" : "am";
    h = h % 12 || 12;
    return `${h}.${m} ${ampm}`;
  }

  // Helper to group lessons by date (YYYY-MM-DD)
  function groupByDate(lessons) {
    return lessons.reduce((acc, lesson) => {
      const dateKey = lesson.reservation_date?.split("T")[0] || "Unknown";
      if (!acc[dateKey]) acc[dateKey] = [];
      acc[dateKey].push(lesson);
      return acc;
    }, {});
  }

  return (
    <div className="page-content page-min-height p-0">
      <div className="flex items-center gap-2 pt-2 pb-4 mb-4">
        <Link to="/all-students" className="text-gray-600 hover:text-primary-500">
          <Icon icon="heroicons-outline:home" className="text-lg" />
        </Link>
        <Icon icon="heroicons-outline:chevron-right" className="text-gray-400 text-sm" />
        <Link to="/all-students" className="text-gray-600 hover:text-primary-500">
          Student List
        </Link>
        <Icon icon="heroicons-outline:chevron-right" className="text-gray-400 text-sm" />
        <span className="text-orange-500 font-medium">Student Profile</span>
      </div>

      
      <Card className="mb-6 p-6 ">
        <div className="bg-orange-50 rounded-xl shadow p-6">
          <div className="flex justify-between items-start">
            <div className="flex gap-10">
              <div className="bg-orange-200 p-4 rounded-xl w-[200px] h-[200px] shadow-md flex flex-col items-center justify-center">
                <div className="w-[140px] h-[140px] rounded-full overflow-hidden border-2 flex items-center justify-center" style={{ borderColor: '#9262F0' }}>
                  {student.profile_image ? (
                    <img 
                      src={replaceUploadUrl(student.profile_image)} 
                      alt={student.name}
                      className="w-[140px] h-[140px]  object-cover"
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = DefaultProfile;
                      }}
                    />
                  ) : (
                    <img 
                      src={DefaultProfile} 
                      alt={student.name}
                      className="w-[140px] h-[140px]  object-cover"
                    />
                  )}
                </div>
              </div>
              </div>

              
              <div className="flex-1 flex flex-row items-start px-6">
              {/* Left column */}
              <div className="flex-1 grid grid-cols-[140px_1fr] gap-y-2 items-center text-[15px]">
                  <span className="font-bold">Name</span>
                  <span className="font-bold">:  {student.name}</span>

                  <span className="font-bold">Japanese Name</span>
                  <span className="font-bold">:  {student.name_jp || "N/A"}</span>

                  <span className="font-bold">Username</span>
                  <span className="font-bold">:  {student.username || "N/A"}</span>
                  
                  <span className="font-bold">Email Address</span>
                  <span className="font-bold">:  {student.email || "<EMAIL>"}</span>

                  <span className="font-bold">Grade</span>
                  <span className="font-bold">:  {student.grade || "4th"}</span>

                  <span className="font-bold">Gender</span>
                  <span className="font-bold">:  {student.gender || "Female"}</span>

                  <span className="font-bold">Age</span>
                  <span className="font-bold">:  {student.age || "N/A"}</span>
                </div>
                <div className="h-[200px] w-[5px] bg-[#FFD9B3] mx-7" />
                <div className="flex-1 grid grid-cols-[120px_1fr] gap-y-2 items-center text-[15px]">
                  <span className="font-bold">Phone</span>
                  <span className="font-bold">:  {student.phone || "N/A"}</span>

                  <span className="font-bold">Nationality</span>
                  <span className="font-bold">:  {student.nationality || "Japanese"} 🇯🇵</span>


                  <span className="font-bold">Hobby</span>
                  <span className="font-bold">:  {student.hobby || "N/A"}</span>

                  <span className="font-bold">Favorite Movie</span>
                  <span className="font-bold">:  {student.favorite_movie || "N/A"}</span>

                  <span className="font-bold">Specialty</span>
                  <span className="font-bold">:  {student.specialty || "N/A"}</span>

                  <span className="font-bold">Rating</span>
                  <span className="font-bold">:  {student.rating ? `${student.rating} (${student.rating_count || 0} reviews)` : "N/A"}</span>
                </div>
              </div>

            <Button
              className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600"
              icon="heroicons:pencil-square"
              onClick={() => setShowEditModal(true)}
            >
              Edit Profile
            </Button>
          </div>
        </div>
<div className="mb-6">
  <div className="flex border-b border-slate-200 mb-4 rounded-xl shadow p-">
    {tabs.map((tab, index) => (
      <div 
        key={index}
        className={`flex-1 px-6 py-3 text-center cursor-pointer border-b-2 transition-all duration-200 ${
          activeTab === index 
            ? "border-orange-500 text-orange-500" 
            : "border-transparent text-slate-700 hover:text-orange-500 hover:border-orange-500"
        } ${index !== 0 ? "border-l border-slate-200" : ""}`}
        onClick={() => setActiveTab(index)}
      >
        <div className="flex items-center justify-center gap-2">
          <Icon icon={tab.icon} className="text-lg" />
          <span className="font-medium">{tab.label}</span>
        </div>
      </div>
    ))}
  </div>

  <div>
    {activeTab === 0 && (
      <div>
        {isLoadingReservations ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
          </div>
        ) : reservationError ? (
          <div className="text-center text-red-500 py-8">Failed to load lesson records.</div>
        ) : reservationData?.data?.length === 0 ? (
          <div className="text-center text-gray-500 py-8">No lesson records found.</div>
        ) : (
          Object.entries(
            groupByDate(
              [...reservationData.data].sort(
                (a, b) => new Date(b.reservation_date) - new Date(a.reservation_date)
              )
            )
          ).map(([date, lessons]) => (
            <div key={date} className="mb-8">
              <div className="text-slate-500 my-4 font-medium">
                {formatFullDateWithOrdinal(date)}
              </div>
              <div className="h-[2px] w-full bg-gradient-to-r from-gray-300 to-transparent mb-4"></div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {lessons.map((lesson, idx) => (
                  <div key={idx}>
                    <div className="border border-orange-200 bg-orange-50 rounded-xl">
                      <div className="border border-orange-200 rounded-xl flex overflow-hidden">
                        {/* Left section */}
                        <div className="flex flex-col items-center justify-center w-[220px] py-6 bg-white">
                          <div className="w-14 h-14 rounded-full overflow-hidden mb-2 border border-slate-200 border-2" style={{ borderColor: '#9262F0' }}>
                            <img
                              src={lesson.teacher.profile_image ? replaceUploadUrl(lesson.teacher.profile_image) : DefaultProfile}
                              alt={lesson.teacher.name || "Teacher"}
                              className="w-full h-full object-cover"
                              onError={e => {
                                e.target.onerror = null;
                                e.target.src = DefaultProfile;
                              }}
                            />
                          </div>
                          <div className="text-base font-medium text-slate-900 text-center">
                            {lesson.teacher.name || "N/A"}
                          </div>
                        </div>
                        {/* Right section */}
                        <div className="flex-1 px-8 py-6 bg-orange-50">
                          <div className="text-lg font-semibold text-slate-900 mb-4">
                            {formatFullDateWithOrdinal(lesson.reservation_date)}
                          </div>
                          <div className="flex items-center text-base text-slate-700 mb-2">
                            <Icon icon="heroicons-outline:clock" className="mr-2 text-slate-500" />
                            <span>
                              {lesson.reservation_start_at
                                ? `${formatTimeHM(lesson.reservation_start_at)} - ${formatTimeHM(lesson.reservation_end_at)}`
                                : "N/A"}
                            </span>
                          </div>
                          {lesson.subject && (
                            <div className="flex items-center text-base text-slate-700">
                              <Icon icon="heroicons-outline:book-open" className="mr-2 text-slate-500" />
                              <span>{lesson.subject}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))
        )}
      </div>
    )}
    {activeTab === 1 && (
      <div>
         <div className="relative w-full max-w-2xl mx-auto my-8">
        <div className="absolute left-0 right-0 top-1/2 z-0 h-[2px] bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200"></div>
        <div className="grid grid-cols-3 divide-x divide-gray-200 relative z-10">
          <div className="flex flex-col items-center py-4">
            <span className="text-2xl font-bold text-gray-800 mb-6">Today</span>
            <span className="text-xl text-gray-700 mt-2">1hour, 30minutes</span>
          </div>
          <div className="flex flex-col items-center py-4">
            <span className="text-2xl font-bold text-gray-800 mb-6">This Week</span>
            <span className="text-xl text-gray-700 mt-2">1hour, 30minutes</span>
          </div>
          <div className="flex flex-col items-center py-4">
            <span className="text-2xl font-bold text-gray-800 mb-6">This Month</span>
            <span className="text-xl text-gray-700 mt-2">1hour, 30minutes</span>
          </div>
        </div>
      </div>

                <div className="flex items-center mb-4">
                <Icon icon="heroicons-outline:calendar" className="mr-2 text-gray-500" />
                <span className="font-medium">October</span>
              </div>

              <h2 className="text-xl font-bold mb-4">Study Hours</h2>
              <div className="mb-6">
                <div className="flex justify-end mb-4">
                  <div className="grid grid-cols-2 gap-x-8 gap-y-4">
                    <span className="flex items-center gap-1">
                      <span className="w-5 h-5 rounded-full bg-[#2563EB] inline-block"></span>
                      <span>Math</span>
                    </span>
                    <span className="flex items-center gap-1">
                      <span className="w-5 h-5 bg-[#8B5CF6] rounded-full inline-block"></span>
                      <span>English</span>
                    </span>
                    <span className="flex items-center gap-1">
                      <span className="w-5 h-5 bg-[#10B981] rounded-full inline-block"></span>
                      <span>Science</span>
                    </span>
                    <span className="flex items-center gap-1">
                      <span className="w-5 h-5 bg-[#F59E0B] rounded-full inline-block"></span>
                      <span>Other</span>
                    </span>
                  </div>
                </div>
                <Chart 
                  options={barChartOptions} 
                  series={barChartSeries} 
                  type="bar" 
                  height={350} 
                />
              </div>
            </div>
          )}
                    {activeTab === 2 && (
                      <div>
                    <div className="mb-6">
                      <div className="flex items-center">
                        <span className="text-2xl font-medium text-orange-500 mr-2">Active Courses</span>
                        <div className="flex-1 h-0.5 bg-gradient-to-r from-orange-400 to-transparent"></div>
                      </div>
                    </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                          {[
                            { title: "Course Title", completion: 80, score: 100, img: "https://img.icons8.com/color/96/000000/classroom.png" },
                            { title: "Course Title2", completion: 80, score: 100, img: "https://img.icons8.com/color/96/000000/graduation-cap.png" }
                          ].map((course, index) => (
                      <Card key={index} className="p-4">
                      <div className="flex items-center">
                        <div className="w-29 h-28 bg-gray-200 rounded-md overflow-hidden flex-shrink-0 mr-6">
                          <img 
                            src={course.img}
                            alt={course.title}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg text-gray-800 mb-1">{course.title}</h3>
                          <div className="mb-1 text-gray-500 text-sm">
                            Completion rate: <span className="font-semibold text-gray-700">{course.completion}%</span>
                          </div>
                          <div className="mb-2 text-gray-500 text-sm">
                            Correct answer rate: <span className="font-semibold text-gray-700">{course.score}%</span>
                          </div>
                          <div className="flex items-center">
                            <div className="flex-1 mr-2">
                              <div className="w-full h-2 bg-gray-200 rounded">
                                <div
                                  className="h-2 bg-orange-500 rounded"
                                  style={{ width: `${course.score}%` }}
                                ></div>
                              </div>
                            </div>
                            <span className="text-gray-700 text-sm font-medium">{course.score}%</span>
                          </div>
                        </div>
                      </div>
                    </Card>
                          ))}
                        </div>
                    <div className="mb-6">
                      <div className="flex items-center">
                        <span className="text-2xl font-medium text-orange-500 mr-2">Upcoming Courses</span>
                        <div className="flex-1 h-0.5 bg-gradient-to-r from-orange-400 to-transparent"></div>
                      </div>
                    </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                          {[
                            { title: "Course Title", completion: 0, score: 0, img: "https://img.icons8.com/color/96/000000/classroom.png" },
                            { title: "Course Title2", completion: 0, score: 0, img: "https://img.icons8.com/color/96/000000/graduation-cap.png" }
                          ].map((course, index) => (
                          <Card key={index} className="p-4">
                      <div className="flex items-center">
                        <div className="w-29 h-28 bg-gray-200 rounded-md overflow-hidden flex-shrink-0 mr-6">
                          <img 
                            src={course.img}
                            alt={course.title}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg text-gray-800 mb-1">{course.title}</h3>
                          <div className="mb-1 text-gray-500 text-sm">
                            Completion rate: <span className="font-semibold text-gray-700">{course.completion}%</span>
                          </div>
                          <div className="mb-2 text-gray-500 text-sm">
                            Correct answer rate: <span className="font-semibold text-gray-700">{course.score}%</span>
                          </div>
                          <div className="flex items-center">
                            <div className="flex-1 mr-2">
                              <div className="w-full h-2 bg-gray-200 rounded">
                                <div
                                  className="h-2 bg-orange-500 rounded"
                                  style={{ width: `${course.score}%` }}
                                ></div>
                              </div>
                            </div>
                            <span className="text-gray-700 text-sm font-medium">{course.score}%</span>
                          </div>
                        </div>
                      </div>
                    </Card>
                          ))}
                        </div>
                    <div className="mb-6">
                      <div className="flex items-center">
                        <span className="text-2xl font-medium text-orange-500 mr-2">Completed Courses</span>
                        <div className="flex-1 h-0.5 bg-gradient-to-r from-orange-400 to-transparent"></div>
                      </div>
                    </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          {[
                            { title: "Course Title", completion: 100, score: 50, img: "https://img.icons8.com/color/96/000000/classroom.png" },
                            { title: "Course Title2", completion: 100, score: 0, img: "https://img.icons8.com/color/96/000000/graduation-cap.png" }
                          ].map((course, index) => (
                          <Card key={index} className="p-4">
                      <div className="flex items-center">
                        <div className="w-29 h-28 bg-gray-200 rounded-md overflow-hidden flex-shrink-0 mr-6">
                          <img 
                            src={course.img}
                            alt={course.title}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg text-gray-800 mb-1">{course.title}</h3>
                          <div className="mb-1 text-gray-500 text-sm">
                            Completion rate: <span className="font-semibold text-gray-700">{course.completion}%</span>
                          </div>
                          <div className="mb-2 text-gray-500 text-sm">
                            Correct answer rate: <span className="font-semibold text-gray-700">{course.score}%</span>
                          </div>
                          <div className="flex items-center">
                            <div className="flex-1 mr-2">
                              <div className="w-full h-2 bg-gray-200 rounded">
                                <div
                                  className="h-2 bg-orange-500 rounded"
                                  style={{ width: `${course.score}%` }}
                                ></div>
                              </div>
                            </div>
                            <span className="text-gray-700 text-sm font-medium">{course.score}%</span>
                          </div>
                        </div>
                      </div>
                    </Card>
                          ))}
                        </div>
                      </div>
                    )}
          
          {activeTab === 3 && (
            <div>
                    <div className="bg-white rounded-xl shadow border  p-0 mb-3">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-orange-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Exam Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Marks
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Class Ranking
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Math
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Science
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Social
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  English
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {[...Array(5)].map((_, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    Exam Name {index + 1}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    22
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    10/35
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    8
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    5
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    5
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    4
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>


              <div className="mb-4">
                <div className="text-sm text-gray-500 mb-2">Marks</div>
                <Line data={data} options={options} />
              </div>
            </div>
          )}
        </div>
      </div>
         </Card>

      {/* Edit Student Modal */}
      <Modal
        activeModal={showEditModal}
        onClose={() => setShowEditModal(false)}
        title="Edit Student Profile"
        centered
        scrollContent
        className="max-w-3xl"
        themeClass="bg-orange-100 dark:bg-slate-800"
      >
        <div className="p-4">
          <SingleStudentCreateForm
            studentData={student}
            isUpdateMode={true}
            onSuccess={handleEditSuccess}
          />
        </div>
      </Modal>
    </div>
  );
};

export default StudentView;

