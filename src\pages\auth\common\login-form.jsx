import React, { useState } from "react";
import Textinput from "@/components/ui/react-hook-form/Textinput";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useNavigate } from "react-router-dom";
import Checkbox from "@/components/ui/react-hook-form/Checkbox";
import Button from "@/components/ui/Button";
import { Link } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { useLoginMutation } from "@/store/api/auth/authApiSlice";
import { handleLogin, setUser } from "@/store/api/auth/authSlice";
import { toast } from "react-toastify";
import { Form, Formik } from "formik";
import InputField from "@/components/ui/formik-form/InputField";
const schema = yup
  .object({
    username: yup
      .string()
      .required("Username is Required"),
    password: yup.string().required("Password is Required"),
  })
  .required();
const LoginForm = () => {
  const [login, { isLoading, isError, error, isSuccess }] = useLoginMutation();

  const dispatch = useDispatch();

  const {
    register,
    formState: { errors },
    handleSubmit,
  } = useForm({
    resolver: yupResolver(schema),
    //
    mode: "all",
  });
  const navigate = useNavigate();
  const onSubmit = async (data) => {
    try {
      const res = await login(data);
      if (res?.data?.status === 200) {
        dispatch(handleLogin(res?.data));
        toast.success(res?.data?.message || "Login successful");
        navigate("/dashboard");
      } else {
        toast.error(res?.data?.message || "Login failed");
      }
    } catch (error) {
      toast.error(error.message);
    }
  };

  const [checked, setChecked] = useState(false);

  return (
    <Formik
      initialValues={{
        username: "",
        password: "",
      }}
      validationSchema={schema}
      onSubmit={onSubmit}
    >
      {({ errors, touched, isSubmitting, handleChange, values }) => (
        <Form className="space-y-4">
          <InputField
            name="username"
            label="Email"
            type="text"
            placeholder="Enter your email"
            error={
              errors.username && touched.username
                ? errors.username
                : ""
            }
            className="h-[56px] rounded-md"
            onChange={handleChange}
            value={values.username}
          />

          <InputField
            name="password"
            label="Password"
            type="password"
            placeholder="Enter your password"
            error={errors.password && touched.password ? errors.password : ""}
            className="h-[56px] rounded-md"
            hasicon={true}
            onChange={handleChange}
            value={values.password}
          />

          <div className="flex-wrap flex justify-between">
          <label className="flex items-center space-x-2">
        <input
          type="checkbox"
          checked={checked}
          onChange={() => setChecked(!checked)}
          className="form-checkbox text-primary-600 accent-primary-600 h-4 w-4"
          />
          <span className="text-sm">Remember me</span>
          </label>
            <Link
              to="/forgot-password"
              className="text-sm text-danger-600 cursor-pointer underline decoration-1"
            >
              Forgot Password?{" "}
            </Link>
          </div>

          <Button
            type="submit"
            text="Login"
            className="bg-secondary-200 block w-full text-center h-[48px] text-black-600 hover:bg-primary-900 hover:text-white"
            isLoading={isSubmitting}
          />
        </Form>
      )}
    </Formik>
  );
};

export default LoginForm;
