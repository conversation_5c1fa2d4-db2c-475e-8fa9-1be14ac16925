import React, { useState, useEffect } from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import Button from "@/components/ui/Button";
import { usePostDataMutation, usePutDataMutation, useFetchDataQuery } from "@/store/api/apiSlice";
import { toast } from "react-toastify";
import { Icon } from "@iconify/react";
import InputSelect from "@/components/ui/formik-form/InputSelect";

const TeacherForm = ({ teacherData, isUpdateMode = false, onSuccess }) => {
  const [postData] = usePostDataMutation();
  const [putData] = usePutDataMutation();
  const [previewImage, setPreviewImage] = useState(null);
  const [uploadImage, setUploadImage] = useState(null);
  const { data: countriesData } = useFetchDataQuery("countries");
  const [countryOptions, setCountryOptions] = useState([]);

  useEffect(() => {
    if (teacherData?.avatar) {
      setPreviewImage(teacherData.avatar);
    }
  }, [teacherData]);

  useEffect(() => {
    if (countriesData?.data) {
      const options = countriesData.data.map(country => ({
        value: String(country.id),
        label: country.name
      }));
      setCountryOptions(options);
    }
  }, [countriesData]);

  const validationSchema = Yup.object().shape({
    name: Yup.string().required("Teacher name is required"),
    name_jp: Yup.string().required("Japanese name is required"),
    username: Yup.string().required("Username is required"),
    email: Yup.string().email("Invalid email").required("Email is required"),
    password: isUpdateMode ? Yup.string() : Yup.string().required("Password is required"),
    phone: Yup.string(),
    gender: Yup.string(),
    age: Yup.number().positive("Age must be positive"),
    country_id: Yup.string().required("Country is required"),
    favorite_movie: Yup.string(),
    hobbies: Yup.string(),
    specialty: Yup.string(),
  });

  const getInitialValues = () => {
    if (isUpdateMode && teacherData) {
      return {
        name: teacherData.name || "",
        name_jp: teacherData.name_jp || "",
        username: teacherData.username || "",
        email: teacherData.email || "",
        password: "",
        phone: teacherData.phone || "",
        gender: teacherData.gender || "",
        age: teacherData.age || "",
        country_id: teacherData.country_id ? String(teacherData.country_id) : "",
        favorite_movie: teacherData.favorite_movie || "",
        hobbies: teacherData.hobbies || "",
        specialty: teacherData.specialty || "",
      };
    }
    
    return {
      name: "",
      name_jp: "",
      username: "",
      email: "",
      password: "",
      phone: "",
      gender: "",
      age: "",
      country_id: "",
      favorite_movie: "",
      hobbies: "",
      specialty: "",
    };
  };

  const handleSubmit = async (values, { setSubmitting }) => {
    setSubmitting(true);
    
    const formData = new FormData();
    
    // Add all form fields to formData
    Object.keys(values).forEach(key => {
      if (values[key] !== null && values[key] !== undefined && values[key] !== '') {
        formData.append(key, values[key]);
      }
    });
    
    // Add profile image if it exists
    if (uploadImage) {
      formData.append("profile_image", uploadImage);
    }
    
    // Add user_type for teacher
    formData.append('user_type', 'teacher');
    
    try {
      let response;
      
      if (isUpdateMode) {
        // For update, add _method field for Laravel
        formData.append('_method', 'PUT');
        const endpoint = `/users/${teacherData.id}`;
        response = await postData({ url: endpoint, body: formData }).unwrap();
      } else {
        // For create
        response = await postData({ url: '/users', body: formData }).unwrap();
      }
        
      if (response?.status === 200) {
        toast.success(isUpdateMode ? 'Teacher updated successfully' : 'Teacher created successfully');
        if (onSuccess) onSuccess();
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error(error?.data?.message || 'An error occurred while saving teacher data');
    } finally {
      setSubmitting(false);
    }
  };

  const handleImageDrop = (e) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file && file.type.match('image.*')) {
      setPreviewImage(file);
      setUploadImage(file);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  return (
    <Formik
      initialValues={getInitialValues()}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
    >
      {({ isSubmitting, setFieldValue }) => (
        <Form className="p-4">
          {/* Image Upload Area */}
          <div 
            className="border-2 border-dashed border-gray-300 rounded-md p-6 mb-6 text-center"
            onDrop={handleImageDrop}
            onDragOver={handleDragOver}
          >
            {previewImage ? (
              <div className="flex justify-center">
                <div className="relative inline-block">
                  <img 
                    src={typeof previewImage === 'string' ? previewImage : URL.createObjectURL(previewImage)} 
                    alt="Profile" 
                    className="w-24 h-24 rounded-md object-cover"
                  />
                  <button
                    type="button"
                    onClick={() => {
                      setPreviewImage(null);
                      setUploadImage(null);
                    }}
                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1"
                  >
                    <Icon icon="heroicons-outline:x" className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ) : (
              <>
                <Icon icon="heroicons-outline:upload" className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-2 text-sm text-indigo-600 font-medium">
                  Upload an image or drag and drop
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  PNG, JPG, GIF up to 10MB
                </p>
                <input
                  type="file"
                  accept="image/*"
                  className="hidden"
                  id="profile-image"
                  onChange={(e) => {
                    const file = e.target.files[0];
                    if (file) {
                      setPreviewImage(file);
                      setUploadImage(file);
                    }
                  }}
                />
                <label
                  htmlFor="profile-image"
                  className="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 cursor-pointer"
                >
                  Select file
                </label>
              </>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Teacher name */}
            <div className="mb-4">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Teacher name<span className="text-red-500">*</span>
              </label>
              <Field
                type="text"
                name="name"
                placeholder="Enter Teacher name"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              <ErrorMessage name="name" component="div" className="text-red-500 text-xs mt-1" />
            </div>

            {/* Teacher Japanese name */}
            <div className="mb-4">
              <label htmlFor="name_jp" className="block text-sm font-medium text-gray-700 mb-1">
                Teacher Japanese name
              </label>
              <Field
                type="text"
                name="name_jp"
                placeholder="Enter Teacher Japanese name"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              <ErrorMessage name="name_jp" component="div" className="text-red-500 text-xs mt-1" />
            </div>

            {/* Username */}
            <div className="mb-4">
              <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
                Username<span className="text-red-500">*</span>
              </label>
              <Field
                type="text"
                name="username"
                placeholder="Enter Username"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              <ErrorMessage name="username" component="div" className="text-red-500 text-xs mt-1" />
            </div>

            {/* Contact No. */}
            <div className="mb-4">
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                Contact No.
              </label>
              <Field
                type="text"
                name="phone"
                placeholder="Enter contact no."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              <ErrorMessage name="phone" component="div" className="text-red-500 text-xs mt-1" />
            </div>

            {/* Email Address */}
            <div className="mb-4">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email Address<span className="text-red-500">*</span>
              </label>
              <Field
                type="email"
                name="email"
                placeholder="Enter email"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              <ErrorMessage name="email" component="div" className="text-red-500 text-xs mt-1" />
            </div>

            {/* Password */}
            <div className="mb-4">
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                Password{!isUpdateMode && <span className="text-red-500">*</span>}
              </label>
              <Field
                type="password"
                name="password"
                placeholder="Enter password"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              <ErrorMessage name="password" component="div" className="text-red-500 text-xs mt-1" />
            </div>

            {/* Gender */}
            <div className="mb-4">
              <label htmlFor="gender" className="block text-sm font-medium text-gray-700 mb-1">
                Gender
              </label>
              <Field
                as="select"
                name="gender"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              >
                <option value="">Select gender</option>
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="other">Other</option>
              </Field>
              <ErrorMessage name="gender" component="div" className="text-red-500 text-xs mt-1" />
            </div>

            {/* Age */}
            <div className="mb-4">
              <label htmlFor="age" className="block text-sm font-medium text-gray-700 mb-1">
                Age
              </label>
              <Field
                type="number"
                name="age"
                placeholder="Enter age"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              <ErrorMessage name="age" component="div" className="text-red-500 text-xs mt-1" />
            </div>

            {/* Country */}
            <div className="mb-4">
              <InputSelect
                label="Country"
                name="country_id"
                options={countryOptions}
                placeholder="Select country"
                required={true}
              />
            </div>

            {/* Favorite Movie */}
            <div className="mb-4">
              <label htmlFor="favorite_movie" className="block text-sm font-medium text-gray-700 mb-1">
                Favorite Movie
              </label>
              <Field
                type="text"
                name="favorite_movie"
                placeholder="Enter favorite movie"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              <ErrorMessage name="favorite_movie" component="div" className="text-red-500 text-xs mt-1" />
            </div>

            {/* Teacher Hobbies */}
            <div className="mb-4">
              <label htmlFor="hobbies" className="block text-sm font-medium text-gray-700 mb-1">
                Teacher Hobbies
              </label>
              <Field
                type="text"
                name="hobbies"
                placeholder="Enter hobbies"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              <ErrorMessage name="hobbies" component="div" className="text-red-500 text-xs mt-1" />
            </div>

            {/* Teacher Specialty */}
            <div className="mb-4">
              <label htmlFor="specialty" className="block text-sm font-medium text-gray-700 mb-1">
                Teacher Specialty
              </label>
              <Field
                type="text"
                name="specialty"
                placeholder="Enter specialty"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              />
              <ErrorMessage name="specialty" component="div" className="text-red-500 text-xs mt-1" />
            </div>
          </div>

          <div className="flex justify-end mt-6">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-orange-500 text-white px-6 py-2 rounded-md hover:bg-orange-600 transition duration-300"
            >
              {isSubmitting ? "Submitting..." : "Submit"}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default TeacherForm;
