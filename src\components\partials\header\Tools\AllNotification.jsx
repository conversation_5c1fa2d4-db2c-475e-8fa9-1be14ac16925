import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { notifications } from "@/constant/data";
import { CheckCircle } from "lucide-react";
import { Icon } from '@iconify/react';
import Button from '@/components/ui/Button';
import { setBreadcrumbs, setPageTitle } from "@/store/common/commonSlice";
import { useDispatch } from 'react-redux';
import { useSelector } from 'react-redux';


const AllNotification = () => {

    const dispatch = useDispatch();
    const { customizer } = useSelector((state) => state.layout);
    const [currentDrawerTitle, setCurrentDrawerTitle] = useState(null);
    useEffect(() => {
        const breadCrumb = [
            {
                label: "Notification",
                path: "#",
            },
        ]
        dispatch(setBreadcrumbs(breadCrumb));
        dispatch(setPageTitle("Notification List"));
    }, [dispatch]);

    const [currentPage, setCurrentPage] = useState(1);
    const [notificationsPerPage, setNotificationsPerPage] = useState(10);
    const [dummyData, setDummyData] = useState([]); // Assuming the notifications data will come here

    // Assuming this is fetching the notifications from an API or local storage
    useEffect(() => {
        // Fetch or load notification data here and setDummyData accordingly
        setDummyData(notifications); // Assuming 'notifications' is imported
    }, []);

    const handlePageChange = (newPage) => {
        if (newPage >= 1 && newPage <= calculateTotalPages()) {
            setCurrentPage(newPage);
        }
    };

    const itemsPerPage = 10;  // Define how many items per page you want to display
    const totalItems = 150;   // Total number of items (you can fetch this dynamically if needed)

    const calculateTotalPages = () => {
        return Math.ceil(totalItems / itemsPerPage);
    };

    const handlePerPageChange = (e) => {
        setNotificationsPerPage(Number(e.target.value));
        setCurrentPage(1); // Reset to page 1 when changing per page value
    };

    const displayedNotifications = dummyData?.slice(
        (currentPage - 1) * notificationsPerPage,
        currentPage * notificationsPerPage
    );

    const onclickNotificationView = (id) => {
        // Handle the logic for when a notification is clicked (viewing or marking as read)
        console.log("Notification clicked:", id);
    };

    return (
        <div className="bg-white dark:bg-slate-800  flex-1 p-6">

            <div className="flex justify-between items-center mt-6">
                <p className="text-sm text-gray-600">
                    Unread(77)
                </p>

                <p className="px-3 py-1 text-gray-600 rounded-full text-sm flex items-center space-x-1">
                    <Icon icon="mdi:check-all" width="24" height="24" />
                    <span>Mark all Read</span>
                </p>

            </div>

            <div className="bg-gray-200 dark:bg-slate-800 shadow-md rounded-lg flex-1 p-6 space-y-3">

                {dummyData?.length === 0 && (
                    <div className="mt-4 bg-gray-100 rounded-md shadow-md p-4">
                        <p className="text-sm text-gray-600 text-center">No notifications found.</p>
                    </div>
                )}
                {dummyData?.length > 0 && (
                    <>
                        {displayedNotifications?.map((item, i) => (
                            <div
                                key={i}
                                className={`flex p-4 shadow-md rounded-lg ${!item?.is_read ? 'bg-white' : 'bg-gray-300'} items-center cursor-pointer`}
                                onClick={() => onclickNotificationView(item?.id)}
                            >
                                <div className="flex-shrink-0">
                                    <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <Icon icon="clarity:notification-solid" className="text-blue-400 h-5 w-5" />
                                    </div>
                                </div>
                                <div className="ml-5">
                                    <p className="text-sm font-medium">{item?.message}</p>
                                    <div className="flex items-start gap-2 flex-col">
                                        <p className="text-sm">{item?.title}</p>
                                        <div className="flex items-start gap-2">
                                            <Icon icon="mdi:clock-outline" className="text-blue-500" />
                                            <p className="text-sm">{i + 1} minutes ago</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </>
                )}


            </div>


            <div className="flex items-center justify-between mt-4">
                <p className="text-sm text-gray-600 ">
                    {((currentPage - 1) * itemsPerPage) + 1} - {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems}
                </p>

                <div className="flex-grow flex justify-center items-center space-x-2">

                    <Button
                        onClick={() => handlePageChange(currentPage - 1)}
                        className="px-3 py-1 bg-blue-200 rounded-full border border-gray-300 text-sm text-gray-600 hover:bg-gray-100"
                    >
                        &lsaquo;
                    </Button>

                    <Button
                        onClick={() => handlePageChange(currentPage - 1)}
                        className="px-3 py-1  bg-gray-100 rounded-full border border-gray-300 text-sm text-gray-600 hover:bg-gray-100"
                    >
                        1
                    </Button>
                    <Button
                        onClick={() => handlePageChange(currentPage - 1)}
                        className="px-3 py-1  bg-gray-100 rounded-full border border-gray-300 text-sm text-gray-600 hover:bg-gray-100"
                    >
                        2
                    </Button>
                    <Button
                        onClick={() => handlePageChange(currentPage - 1)}
                        className="px-3 py-1  bg-gray-100 rounded-full border border-gray-300 text-sm text-gray-600 hover:bg-gray-100"
                    >
                        3
                    </Button>
                    <Button
                        onClick={() => handlePageChange(currentPage + 1)}
                        className="px-3 py-1  bg-gray-100 rounded-full border border-gray-300 text-sm text-gray-600 hover:bg-gray-100"
                    >
                        4
                    </Button>
                    <Button
                        onClick={() => handlePageChange(currentPage + 1)}
                        className="px-3 py-1 bg-gray-100 rounded-full border border-gray-300 text-sm text-gray-600 hover:bg-gray-100"
                    >
                        5
                    </Button>

                    <Button
                        onClick={() => handlePageChange(currentPage + 1)}
                        className="px-3 py-1 bg-blue-200 rounded-full border border-gray-300 text-sm text-gray-600 hover:bg-gray-100"
                    >
                        &rsaquo;
                    </Button>

                </div>
                <div className="flex items-center">
                    <p className="text-sm text-gray-600 mr-2">Rows per page: </p>
                    <select
                        value={notificationsPerPage}
                        onChange={handlePerPageChange}
                        className="py-1 bg-white text-sm text-gray-600"
                    >
                        <option value={10}>10</option>
                        <option value={20}>20</option>
                        <option value={30}>30</option>
                        <option value={50}>50</option>
                    </select>
                </div>

            </div>

        </div>


    );
};

export default AllNotification;
