import React, { useEffect, Suspense } from "react";
import { Outlet, useNavigate, useLocation } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import Loading from "@/components/Loading";
import { useDispatch } from "react-redux";
import { useSelector } from "react-redux";

const AuthLayout = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isAuth, user } = useSelector((state) => state.auth);

  useEffect(() => {
    if (isAuth) {
      navigate("/dashboard");
    }
  }, [isAuth, navigate]);

  useEffect(() => {
    const classNames = ["bg-gradient-to-r", "from-[#FFA84D]", "to-[#D3750A]"];
    classNames.forEach((className) => document.body.classList.add(className));
  
    return () => {
      classNames.forEach((className) => document.body.classList.remove(className));
    };
  }, []);
  

  return (
    <>
      <div className="h-full">
        <div className="container h-full">
          <Suspense fallback={<Loading />}>
            <ToastContainer />
            {<Outlet />}
          </Suspense>
        </div>
      </div>
    </>
  )
};

export default AuthLayout;
