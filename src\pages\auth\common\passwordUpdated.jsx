import React, { useEffect } from "react";
import { Link } from "react-router-dom";
import Logo from "@/assets/images/logo/logo.png";
import ForgotEmail from "./forgotEmail";
import PasswordSet from "@/pages/auth/common/resetPassword";
import ForgotLockImage from "@/assets/forgotLock.png";

const passwordUpdated = () => {
  return (
    <div className="p-20">
      <div className="mb-4 inline-block">
        <Link to="/" className="block">
          <img src={Logo} alt="logo" />
        </Link>
      </div>
      <div
        className="grid-cols-12 gap-8 bg-white p-10 shadow rounded-md"
        style={{
          boxShadow: "-8px 16px 32px 0px rgba(0, 0, 0, 0.4)",
        }}
      >
        <div>
          <div className="h-full flex flex-col justify-center">
            <div>
              <div className="mb-5">
                <h1 className="text-4xl font-bold text-black-700 mb-2">
                  Continue as Password Updated
                </h1>
              </div>
              <div className="grid grid-cols-12 gap-6 items-center">
                <div className="col-span-6">
                  <PasswordSet />
                </div>
                <div className="col-span-6 flex items-center justify-center">
                  <img src={ForgotLockImage} alt="Lock Images" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default passwordUpdated;
