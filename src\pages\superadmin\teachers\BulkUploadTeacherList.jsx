import React, { useEffect, useState } from 'react'
import BulkUsersUpload from '@/components/teachersStudents/BulkUsersUpload';
import { setBreadcrumbs, setPageTitle } from '@/store/common/commonSlice';
import { useDispatch } from 'react-redux';
import { schoolsBulkUpload, teachersBulkUpload } from '@/constant/apiUrls';
import { usePostDataMutation } from '@/store/api/apiSlice';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import { LoaderCircle } from 'lucide-react';
import Loading from '@/components/Loading';
const BulkUploadTeacherList = () => {
  const dispatch = useDispatch();
  const [loader, setLoader] = useState(false);
  const [postData, { isLoading: isSubmitting }] = usePostDataMutation();
  const navigate = useNavigate();
  useEffect(() => {
    const breadCrumb = [
      { label: "Teachers", path: "/all-teachers" },
      { label: "Teachers Bulk Upload", path: "#" }
    ];
    dispatch(setBreadcrumbs(breadCrumb));
    dispatch(setPageTitle({
      title: "Teachers Bulk Upload",
      isBackButton: true
    }));
  }, [dispatch]);

  const [teacherKeys, setStudentKeys] = useState(Object.keys({
        name: "",
        name_jp: "",
        username: "",
        profile_image: "",
        phone: "",
        email: "",
        school_id: "",
        grade_id: "",
        country_id: "",
        hobby: "",
        favorite_movie: "",
        specialty: "",
        user_type: "student",
        favorite_added: false,
        is_active: true,
        password: "",
      }));

  const uploadSchoolData = async (data) => {
      setLoader(true);
      let payload = {
        "user_type":"teacher",
        "schools": data
      }

       try {
        const response = await postData({ url: teachersBulkUpload, body: payload }).unwrap().then((res) => {
          if (res?.status === 200) {
            toast.success(res?.data?.message);
            setLoader(false);
            navigate("/all-schools");
          }
          
        });
      } catch (error) {
        console.log(error?.error?.error);
      }
    }

     

    const columns = [
    {
      header: 'Name',
      accessorKey: 'name',
      cell: info => (
        <span className={info.row.original.duplicate ? 'text-red-500' : ''}>
          {info.getValue()}
        </span>
      ),
    },
    {
      header: 'Email',
      accessorKey: 'email',
      cell: info => (
        <span className={info.row.original.duplicate ? 'text-red-500' : ''}>
          {info.getValue()}
        </span>
      ),
    },
    { header: 'Phone', accessorKey: 'phone' },
    { header: 'School ID', accessorKey: 'school_id' },
    {
      header: 'Duplicate?',
      accessorKey: 'duplicate',
      cell: info =>
        info.getValue() ? (
          <span className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded">Yes</span>
        ) : (
          <span className="text-xs bg-green-100 text-green-600 px-2 py-1 rounded">No</span>
        ),
    },
    {
      header: 'Action',
      accessorKey: 'id',
      // The cell implementation will be provided by BulkUsersUpload component
    },
  ];

  return (
    <div>
      {isSubmitting && <Loading />}
      {!isSubmitting &&  
      <>
        <BulkUsersUpload uploadData={uploadSchoolData} columns={columns} excelUrl={import.meta.env.VITE_TEACHER_UPLOAD_EXCEL_FILE} dataKeys={teacherKeys} compareKeys={["username", "phone"]} />
      </>
      }
    </div>

  )
}

export default BulkUploadTeacherList
