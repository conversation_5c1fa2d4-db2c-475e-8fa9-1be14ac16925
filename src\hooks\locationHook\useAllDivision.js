import { useFetchDataQuery } from "@/store/api/apiSlice";

const useAllDivision = () => {
    const { data: divisionData, error: divisionError, isLoading: divisionIsLoading } = useFetchDataQuery("/open/division-list");

    // Transform API response into dropdown-friendly format
    const divisionOptions = divisionData?.data?.map((item) => ({
        value: item.id,
        label: item.name,
    })) || [];

    return { divisionOptions, divisionError, divisionIsLoading };
};

export default useAllDivision;
