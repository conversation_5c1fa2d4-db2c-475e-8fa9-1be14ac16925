import React, { useEffect, useState } from "react";
import Card from "@/components/ui/Card";
import Icon from "@/components/ui/Icon";
import { useDispatch } from "react-redux";
import { setBreadcrumbs, setPageTitle } from "@/store/common/commonSlice";
import Button from "@/components/ui/Button";
import ProfileDetails from "./ProfileDetails";
import ProfileDetailsAddress from "./PasswordInformation";
import ChangePassword from "./PasswordInformation";
import LogedinDevices from "./LogedinDevices";
import ProfileDetailsCard from "@/components/ui/ProfileDetailsCard";
import PageMenuBlock from "@/components/ui/PageMenuBlock";
import { useFetchDataQuery } from "@/store/api/apiSlice";

const Settings = () => {
  const dispatch = useDispatch();
  const [activeMenu, setActiveMenu] = useState("personal_information");
  const {
      data,
      error,
      isLoading: isFetching,
    } = useFetchDataQuery("/admin/user-information");

  useEffect(() => {
    const breadCrumb = [
      {
        label: "Settings",
        path: "#",
      },
    ]
    dispatch(setBreadcrumbs(breadCrumb));
    dispatch(setPageTitle({
      title : "Settings",
      isBackButton : true
    }));
  }, [dispatch]);

  const menuList = [
    { title: "Personal Information", id: "personal_information" },
    { title: "security & privacy", id: "security_privacy" },
    // { title: "logged in devices", id: "logged_in_devices" },
  ];

  const profileData = {
    name: data?.data?.name || "No Data Found",
    role: "Admin",
    address: data?.data?.address || "No Data Found",
    image: data?.data?.profile_image || "",
  }

  const onclickChangeMenu = (id) => {
    setActiveMenu(id);
  };

  return (
    <>
      <div className="profile-page">
        {/* Profile Card */}
        <ProfileDetailsCard profileData={profileData} profileType="basic" />

        <div className="grid grid-cols-12 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-3 col-span-12">
            <PageMenuBlock menuList={menuList} onclickChangeMenu={(id) => onclickChangeMenu(id)} />
          </div>

          {/* Main Content */}
          <div className="lg:col-span-9 col-span-12">
            {activeMenu === "personal_information" && <ProfileDetails data={data?.data} />}
            {activeMenu === "security_privacy" && <ChangePassword data={data?.data} />}
            {activeMenu === "logged_in_devices" && <LogedinDevices />}
          </div>
        </div>
      </div>
    </>
  );
};

export default Settings;
