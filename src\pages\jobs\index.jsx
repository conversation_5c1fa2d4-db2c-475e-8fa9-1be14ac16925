import React, { useEffect, useState } from "react";
import { setBreadcrumbs, setPageTitle } from "@/store/common/commonSlice";
import { useDispatch, useSelector } from "react-redux";
import TableAndGridViewer from "@/components/ui/TableAndGridViewer";
import Dropdown from "@/components/ui/react-hook-form/Dropdown";
import Button from "@/components/ui/Button";
import GlobalDrawer from "@/components/partials/globalDrawer";
import { handleCustomizer } from "@/store/layout";
import { useFetchDataQuery, usePostDataMutation } from "@/store/api/apiSlice";
import JobFilter from "./jobProfile/JobFilter";
import usePagination from '@/hooks/paginationHook/usePagination';

const Jobs = () => {
  const dispatch = useDispatch();
  const [jobListUrl, setJobListUrl] = useState("admin/applied-list");
  const { customizer } = useSelector((state) => state.layout);
  const [currentDrawerTitle, setCurrentDrawerTitle] = useState(null);

  const {
    data: apiData,
    isLoading,
    currentPage,
    totalPages,
    handlePageChange,
    error,
    handleReset,
    handleItemPerPage,
  } = usePagination(jobListUrl, useFetchDataQuery);

  useEffect(() => {
    const breadCrumb = [{ label: "Jobs", path: "#" }];
    dispatch(setBreadcrumbs(breadCrumb));
    dispatch(setPageTitle("Applied Jobs List"));
  }, [dispatch]);

  const columns = [
    {
      header: "#",
      accessorKey: "number",
      enableSorting: true,
      sortingFn: (rowA, rowB) => rowA.index - rowB.index,
      cell: ({ row: { index } }) => index + 1,
    },
    {
      header: "JOB TITLE WITH CODE",
      accessorKey: "job_title",
      cell: ({ row }) => (
        <div className="flex flex-col items-start space-y-2">
          <div className="font-bold">{row.original.job_title}</div>
          <div>{row.original.job_id}</div>
        </div>
      ),
    },
    {
      header: "Posted by",
      accessorKey: "posted_by",
      cell: ({ row }) => (
        <div className="flex flex-col items-start space-y-2">
          <div className="font-bold">{row.original.kid?.name || "N/A"}</div>
        </div>
      ),
    },
    {
      header: "Medium",
      accessorKey: "medium",
      cell: ({ row }) => (
        <div className="font-bold">
          {row.original.medium?.title_en || "N/A"}
        </div>
      ),
    },
    // {
    //   header: "Tutoring Plan",
    //   accessorKey: "tutoring_time",
    //   cell: ({ row }) => (
    //     <div className="font-bold">{row.original.tutoring_time || "N/A"}</div>
    //   ),
    // },
    {
      header: "Class",
      accessorKey: "class",
      cell: ({ row }) => (
        <div className="font-bold">{row.original.grade?.name_en || "N/A"}</div>
      ),
    },
    {
      header: "Days",
      accessorKey: "slot_days",
      cell: ({ row }) => (
        <div className="font-bold">
          {row.original.slot_days} Days
        </div>
      ),
    },
    {
      header: "Salary",
      accessorKey: "salary_amount",
      cell: ({ row }) => (
        <div className="font-bold">
          {row.original.salary_amount} / {row.original.salary_type}
        </div>
      ),
    },
    {
      header: "Time & Location",
      accessorKey: "tutoring_time",
      cell: ({ row }) => (
        <div className="flex flex-col items-start space-y-2">
          <div className="font-bold">{row.original.tutoring_time}</div>
          <div>{`Area ID: ${row.original.area_id}, District ID: ${row.original.district_id}`}</div>
        </div>
      ),
    },
    {
      header: "Student Gender",
      accessorKey: "student_gender",
      cell: ({ row }) => (
        <div className="font-bold">{row.original.kid?.gender || "N/A"}</div>
      ),
    },
    {
      header: "Tutor Gender",
      accessorKey: "gender",
      cell: ({ row }) => <div className="font-bold">{row.original.gender}</div>,
    },
    {
      header: "Status",
      accessorKey: "status",
      cell: ({ row }) => (
        <div className="font-bold">{row.original.status === 'accepted' ? 'Accepted' : 'Pending'}</div>
      ),
    },

    // {
    //   header: "Hire",
    //   accessorKey: "is_applied",
    //   cell: ({ row }) => {
    //     const isApplied = row.original.is_applied;
    //     return (
    //       <Button
    //         type="button"
    //         className={`px-3 py-1 rounded text-white text-sm ${
    //           isApplied ? "bg-green-600" : "bg-red-500"
    //         }`}
    //       >
    //         {isApplied ? "Hired" : "Not Hired"}
    //       </Button>
    //     );
    //   },
    // },
    {
      header: "Hire",
      accessorKey: "is_applied",
      cell: ({ row }) => {
        const dispatch = useDispatch();
        const [postData, { isLoading }] = usePostDataMutation();
        const isApplied = row.original.status;
        const jobId = row.original.id;

        const handleHireToggle = async () => {
          try {
            const response = await postData({
              url: `/admin/hire-tutor/${jobId}`,
              body: {},
            }).unwrap();
            if (response?.status === 200) {
              dispatch(apiSlice.util.invalidateTags(["Data"]));
            }
          } catch (error) {
            console.error(error);
          }
        };
        return (
          <Button
            type="button"
            onClick={handleHireToggle}
            disabled={isLoading}
            className={`px-3 py-1 rounded text-white text-sm ${
              isApplied === 'accepted' ? "bg-green-600" : "bg-red-500"
            }`}
          >
            {isApplied === 'accepted' ? "Hired" : "Hire"}
          </Button>
        );
      },
    },
  ];

  const dropdownFilters = (
    <div className="flex items-center gap-3">
      <Dropdown
        label="Select option"
        wrapperClass="w-40 bg-gray-200 dark:bg-slate-700 p-2 rounded-md"
        labelClass="text-slate-500 dark:text-slate-400 text-sm leading-6 text-left flex items-center justify-between"
        items={[
          { label: "All", value: "all" },
          { label: "Active", value: "active" },
          { label: "Inactive", value: "inactive" },
        ]}
      />
      <Button
        className="border weight-none border-secondary-950 text-slate-400 px-4 py-2 rounded-md hover:bg-blue-500 hover:text-white transition duration-300"
        icon="mage:filter-fill"
        onClick={() => {
          setCurrentDrawerTitle("Filter");
          dispatch(handleCustomizer(true));
        }}
      >
        Filters
      </Button>
    </div>
  );

  const actionButtons = (
    <div className="flex items-center md:justify-end gap-3 flex-wrap" />
  );

  return (
    <>
      <div className="profile-page">
        <TableAndGridViewer
          columns={columns}
          data={apiData?.data?.data || []}
          pagination={{
            total: apiData?.data?.last_page || 0,
            perPage: apiData?.data?.per_page || 10,
            currentPage: apiData?.data?.current_page || 1,
            totalRecords: apiData?.data?.total || 0,
            dataTo: apiData?.data?.to || 0,
            dataFrom: apiData?.data?.from || 0,
          }}
          dropdownFilters={dropdownFilters}
          actionButtons={actionButtons}
          gridStyle="third"
          gridColumns={3}
          showSearch={false}
          gridSwitcher={false}
          handlePageChange={handlePageChange}
          handleItemPerPage={handleItemPerPage}
        />
      </div>
      {customizer && (
        <GlobalDrawer title={currentDrawerTitle}>
          <JobFilter />
        </GlobalDrawer>
      )}
    </>
  );
};

export default Jobs;
