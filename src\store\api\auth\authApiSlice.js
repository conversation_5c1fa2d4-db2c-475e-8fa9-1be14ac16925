
import { fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { apiSlice } from "../apiSlice";

const API_URL = import.meta.env.VITE_API_URL;

export const authApi = apiSlice.injectEndpoints({
  baseQuery: fetchBaseQuery({
    baseUrl: API_URL,
  }),
  endpoints: (builder) => ({
    registerUser: builder.mutation({
      query: (user) => ({
        url: "register",
        method: "POST",
        body: user,
      }),
    }),
    login: builder.mutation({
      query: (data) => ({
        url: "login",
        method: "POST",
        body: data,
      }),
      transformResponse: (response, meta) => ({
        ...response,
        status: meta?.response?.status,
      }),
    }),
    checkUser: builder.mutation({
      query: (data) => ({
        url: "check-user",
        method: "POST",
        body: data,
      }),
    }),
    verifyOtp: builder.mutation({
      query: (data) => ({
        url: "verify-otp",
        method: "POST",
        body: data,
      }),
    }),

    resentVerifyOtp: builder.mutation({
      query: (data) => ({
        url: "check-user-verification",
        method: "POST",
        body: data,
      }),
    }),


    //forgot password
    forgetPassword: builder.mutation({
      query: (data) => ({
        url: "forget-password",
        method: "POST",
        body: data,
      }),
    }),
  }),
});

export const {
  useRegisterUserMutation,
  useLoginMutation,
  useCheckUserMutation,
  useVerifyOtpMutation,
  useForgetPasswordMutation,
  useResentVerifyOtpMutation
} = authApi;
