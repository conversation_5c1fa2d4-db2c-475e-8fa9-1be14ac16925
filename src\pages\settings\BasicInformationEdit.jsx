import React, { useState } from "react";
import * as Yup from "yup";
import { Form, Formik } from "formik";
import ProfileImageUploadFormik from "@/components/ui/formik-form/ProfileImageUploadFormik";
import Button from "@/components/ui/Button";
import { useDispatch } from "react-redux";
import { handleCustomizer } from "@/store/layout";
import StudentInfoCard from "@/components/ui/StudentInfoCard";
import { Icon } from "@iconify/react";
import { usePostDataMutation } from "@/store/api/apiSlice";
import { toast } from "react-toastify";
import InputField from "@/components/ui/formik-form/InputField";
import InputSelect from "@/components/ui/formik-form/InputSelect";
import Textarea from "@/components/ui/formik-form/FloadingTextarea";
import { formatDateToISO, getProfileInformationFromCookies, replaceUploadUrl, updateProfileInformation } from "@/helper/helperFunction";
import Loading from "@/components/Loading";
import Cookies from "js-cookie";

const validationSchema = Yup.object().shape({
  name: Yup.string()
    .min(2, "Name is too short")
    .max(50, "Name is too long")
    .required("Name is required"),
  fathers_name: Yup.string().required("Father's name is required"),
  primary_number: Yup.string()
    .matches(/^\+?\d{0,15}$/, "Invalid phone number format")
    .required("Phone number is required"),
  mothers_name: Yup.string().required("Mother's name is required"),
  date_of_birth: Yup.string().required("Date of birth is required"),
  email: Yup.string()
    .email("Invalid email format")
    .required("Email is required"),
  gender: Yup.string()
    .oneOf(["Male", "Female", "Other"], "Please select a valid gender")
    .required("Gender is required"),
});

const BasicInformationForm = ({ data, onNext, updateForm }) => {
  const dispatch = useDispatch();
  const [showPreview, setShowPreview] = useState(false);
  const [formData, setFormData] = useState({});
  const [uploadImage, setUploadImage] = useState(null);

  const [postData, { isLoading }] = usePostDataMutation();
  // console.log("data", data);

  const initialValues = {
    profile_image: null,
    name: data?.name || "",
    email: data?.email || "",
    gender: data?.gender || "",
    bio: data?.bio || "",
    blood_group: data?.blood_group || "",
    mothers_name: data?.mothers_name || "",
    fathers_name: data?.fathers_name || "",
    religion: data?.religion || "",
    // date_of_birth: data?.date_of_birth || "",
    date_of_birth: data?.date_of_birth
      ? formatDateToISO(data.date_of_birth)
      : "",
    primary_number: data?.primary_number || "",
    alternate_number: data?.alternate_number || "",
  };

  const handleSubmit = async (values, { setSubmitting, setErrors }) => {
    const formData = new FormData();
    formData.append("name", values.name);
    formData.append("email", values.email);
    formData.append("gender", values.gender);
    formData.append("bio", values.bio);
    formData.append("blood_group", values.blood_group);
    formData.append("mothers_name", values.mothers_name);
    formData.append("fathers_name", values.fathers_name);
    formData.append("religion", values.religion);
    // formData.append("date_of_birth", formatDateToISO(values.date_of_birth));
    formData.append("date_of_birth", formatDateToISO(values.date_of_birth));
    formData.append("primary_number", values.primary_number);
    formData.append("alternate_number", values.alternate_number);
    if (values.profile_image instanceof File) {
      formData.append("profile_image", values.profile_image);
    }

    try {
      const response = await postData({
        url: "/tutor/user-information",
        body: formData,
      }).unwrap();
      if (response?.status === 200) {
        toast.success(response?.data?.message);
        updateProfileInformation(response?.data).then((res) => {
          getProfileInformationFromCookies(dispatch);
        });
        dispatch(handleCustomizer(false));
      }
    } catch (error) {
      toast.error("An unexpected error occurred.");
    }
    setSubmitting(false);
  };

  const handleCloseDrawer = () => {
    dispatch(handleCustomizer(false));
  };

  const handleEdit = () => {
    setShowPreview(false);
  };

  const handleUploadImage = (file) => {
    const previewUrl = URL.createObjectURL(file);
    setUploadImage(previewUrl);
  };

  if (isLoading) return <Loading />;

  return (
    <>
      <div className="bg-white rounded-lg">
        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ values, errors, touched, isSubmitting, setFieldValue }) => (
            <Form className="mt-4">
              {updateForm && values.profileImage !== null ? (
                <>
                  <div className="flex items-center flex-col gap-4">
                    <div className="h-[100px] w-[100px] rounded-full overflow-hidden border-4 border-slate-100">
                      <img
                        src={replaceUploadUrl(data?.profile_image)}
                        alt="Profile"
                        className="h-[100px] w-[100px] object-cover"
                      />
                    </div>
                  </div>
                </>
              ) : (
                ""
              )}
              <ProfileImageUploadFormik
                name="profile_image"
                uploadImage={handleUploadImage}
                imgData={uploadImage}
                required={true}
              />
              {/* <ProfileImageUploadFormik name="profile_image" required={true} /> */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    NAME <span className="text-red-500">*</span>
                  </label>
                  <InputField
                    name="name"
                    type="text"
                    placeholder={initialValues.name}
                    value={values.name}
                    required={true}
                  />
                  {errors.name && touched.name && (
                    <div className="text-red-500 text-base mt-1">
                      {errors.name}
                    </div>
                  )}
                </div>
                {/* Email */}
                <div className="md:col-span-1">
                  <InputField
                    required={true}
                    label="EMAIL"
                    name="email"
                    type="text"
                    placeholder={initialValues.email}
                    value={values.email}
                  />
                  {errors.email && touched.email && (
                    <div className="text-red-500 text-base mt-1">
                      {errors.email}
                    </div>
                  )}
                </div>
                {/* Phone Number */}
                <div className="flex gap-2">
                  <InputField
                    label="Primary Number"
                    name="primary_number"
                    type="text"
                    placeholder={initialValues.primary_number}
                    value={values.primary_number}
                    formPreappend="+88"
                    required={true}
                  />
                </div>
                {/* Alternate Number */}
                <div className="flex gap-2">
                  <InputField
                    label="Alternate Number"
                    name="alternate_number"
                    type="text"
                    placeholder={initialValues.alternate_number}
                    value={values.alternate_number}
                    formPreappend="+88"
                    required={false}
                  />
                </div>
                {/* father's Name */}
                <div>
                  <InputField
                    label="Father'S NAME"
                    name="fathers_name"
                    type="text"
                    placeholder={initialValues.fathers_name}
                    value={values.fathers_name}
                    required={true}
                  />
                </div>
                {/* Mother's Name */}
                <div>
                  <InputField
                    label="MOTHER'S NAME"
                    name="mothers_name"
                    type="text"
                    placeholder={initialValues.mothers_name}
                    value={values.mothers_name}
                    required={true}
                  />
                </div>
                {/* Date of Birth */}
                <div>
                  <InputField
                    label="DATE OF BIRTH"
                    name="date_of_birth"
                    type="date"
                    placeholder="YYYY-MM-DD"
                    required={true}
                  />
                </div>
                {/* Gender */}
                <div>
                  <InputSelect
                    label="GENDER"
                    name="gender"
                    options={[
                      { value: "Male", label: "Male" },
                      { value: "Female", label: "Female" },
                      { value: "Other", label: "Other" },
                    ]}
                    value={values.gender}
                    required={true}
                    error={touched.gender && errors.gender ? errors.gender : ""}
                  />
                  {errors.gender && touched.gender && (
                    <div className="text-red-500 text-base mt-1">
                      {errors.gender}
                    </div>
                  )}
                </div>
                {/* Marital Status */}
                {/* religion */}
                <div>
                  <InputField
                    label="Religion"
                    name="religion"
                    type="text"
                    placeholder={initialValues.religion}
                    value={values.religion}
                    required={false}
                  />
                </div>
                
                {/* Blood group */}
                <div>
                  <InputSelect
                    label="Blood Group"
                    name="blood_group"
                    options={[
                      { value: "A+", label: "A+" },
                      { value: "A-", label: "A-" },
                      { value: "B+", label: "B+" },
                      { value: "B-", label: "B-" },
                      { value: "AB+", label: "AB+" },
                      { value: "AB-", label: "AB-" },
                      { value: "O+", label: "O+" },
                      { value: "O-", label: "O-" },
                    ]}
                    value={values.blood_group}
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 gap-6 mt-5">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Biography
                </label>
                <Textarea
                  name="bio"
                  type="text"
                  placeholder={initialValues.bio}
                  value={values.bio}
                  required={false}
                />
              </div>

              <div className="flex gap-2 items-center justify-end flex-wrap sticky bottom-0 bg-white pt-5 z-2">
                <Button
                  onClick={handleCloseDrawer}
                  type="button"
                  className="bg-secondary-950 text-black-950 hover:bg-secondary-900 hover:text-white transition-all duration-300"
                >
                  Cancel
                </Button>
                <Button
                  icon={!updateForm && "gg:arrow-right"}
                  type="submit"
                  className="bg-primary-900 border border-primary-500  hover:bg-primary-900 text-white transition-all duration-300"
                  iconPosition="right"
                  disabled={isSubmitting}
                >
                  {!updateForm
                    ? isSubmitting
                      ? "Saving..."
                      : "Submit"
                    : isSubmitting
                    ? "Submitting..."
                    : "Submit"}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
      {showPreview && (
        <StudentInfoCard
          data={formData}
          onEdit={handleEdit}
          onClose={handleCloseDrawer}
        />
      )}
    </>
  );
};

export default BasicInformationForm;
