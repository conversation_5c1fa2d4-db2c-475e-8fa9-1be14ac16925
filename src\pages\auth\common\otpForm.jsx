import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as yup from "yup";
import { toast } from "react-toastify";
import Button from "@/components/ui/Button";
import {
  useVerifyOtpMutation,
  useResentVerifyOtpMutation,
} from "@/store/api/auth/authApiSlice";
import InputField from "@/components/ui/formik-form/InputField";
import Cookies from "js-cookie";
import { handleResetPassword } from "@/store/api/auth/authSlice";

const otpForm = () => {
  const [verifyOtp, { isLoading }] = useVerifyOtpMutation();
  const [resentVerifyOtp, { isLoading: isResendLoading }] =
    useResentVerifyOtpMutation();
  const { userCategory } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [time, setTime] = useState(59);
  const [invalidmsg, setinvalidemsg] = useState("");

  const [searchParams] = useSearchParams();
  const from = searchParams.get("from");

  // Timer logic
  useEffect(() => {
    const timer = setInterval(() => {
      if (time > 0) {
        setTime((prevTime) => prevTime - 1);
      }
    }, 1000);
    return () => clearInterval(timer);
  }, [time]);

  // Function to resend OTP using resentVerifyOtp mutation
  const resendOtp = async () => {
    setTime(60); // Reset timer
    try {
      const payload = {
        email_or_username: Cookies.get("emailOrPhoneNumber"),
        user_type: Cookies.get("userCategory"),
      };
      const response = await resentVerifyOtp(payload);

      if (response?.data?.status === true) {
        toast.success("OTP has been resent.");
      } else {
        setinvalidemsg("Failed to resend OTP. Please try again.");
      }
    } catch (error) {
      toast.error(error.message || "Resend OTP failed");
    }
  };

  const schema = yup.object({
    otp1: yup.string().required("OTP is required"),
    otp2: yup.string().required("OTP is required"),
    otp3: yup.string().required("OTP is required"),
    otp4: yup.string().required("OTP is required"),
  });

  const onSubmit = async (values, { setSubmitting }) => {
    const email = Cookies.get("email_or_username");
    const userType = Cookies.get("user_type");

    if (!email) {
      toast.error("Email not found. Please try again.");
      setSubmitting(false);
      return;
    }

    try {
      const payload = {
        email_or_username: email,
        verification_code: `${values.otp1}${values.otp2}${values.otp3}${values.otp4}`,
        user_type: userType || "Admin",
      };

      const response = await verifyOtp(payload);

      if (response?.data?.status === true) {
        toast.success(response?.data?.message || "OTP verified");
        dispatch(handleResetPassword(response?.data));
        navigate("/reset-password");
      } else {
        setinvalidemsg(response?.data?.data?.original?.message);
      }
    } catch (error) {
      toast.error(error.message || "Invalid Credentials");
    }
    setSubmitting(false);
  };

  return (
    <>
      <Formik
        initialValues={{
          otp1: "",
          otp2: "",
          otp3: "",
          otp4: "",
        }}
        validationSchema={schema}
        onSubmit={onSubmit}
      >
        {({
          values,
          errors,
          touched,
          handleChange,
          handleBlur,
          handleSubmit,
          isSubmitting,
        }) => {
          useEffect(() => {
            const inputs = document.querySelectorAll('input[name^="otp"]');
            inputs.forEach((input, index) => {
              input.addEventListener("input", () => {
                if (input.value.length === 1 && index < inputs.length - 1) {
                  inputs[index + 1].focus();
                }
              });
            });

            return () => {
              inputs.forEach((input) => {
                input.removeEventListener("input", () => {});
              });
            };
          }, []);

          return (
            <Form className="space-y-4">
              <div className="grid grid-cols-4 gap-5">
                <InputField
                  type="text"
                  name="otp1"
                  className="text-center border shadow-md rounded-md"
                  onChange={handleChange}
                  onBlur={handleBlur}
                  value={values.otp1}
                  showError={false}
                />
                <InputField
                  type="text"
                  name="otp2"
                  className="text-center border shadow-md rounded-md"
                  onChange={handleChange}
                  onBlur={handleBlur}
                  value={values.otp2}
                  showError={false}
                />
                <InputField
                  type="text"
                  name="otp3"
                  className="text-center border shadow-md rounded-md"
                  onChange={handleChange}
                  onBlur={handleBlur}
                  value={values.otp3}
                  showError={false}
                />
                <InputField
                  type="text"
                  name="otp4"
                  className="text-center border shadow-md rounded-md"
                  onChange={handleChange}
                  onBlur={handleBlur}
                  value={values.otp4}
                  showError={false}
                />
              </div>
              <div className="flex justify-between flex-wrap">
                <p className="text-sm text-red-500 text-left">{invalidmsg}</p>
                <div className="flex">
                  {time > 0 ? (
                    <p className="text-sm text-gray-500 text-right">
                      Send code in 00:{time} minute
                    </p>
                  ) : (
                    <button
                      type="button"
                      className="text-sm text-primary-500 cursor-pointer hover:underline"
                      onClick={resendOtp}
                      disabled={isResendLoading}
                    >
                      {isResendLoading ? "Sending..." : "Resend OTP"}
                    </button>
                  )}
                </div>
              </div>

              <Button
                type="submit"
                text="Continue"
                className="bg-secondary-200 block w-full text-center h-[48px] text-black-600 hover:bg-primary-900 hover:text-white"
                isLoading={isSubmitting || isLoading}
              />
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default otpForm;
