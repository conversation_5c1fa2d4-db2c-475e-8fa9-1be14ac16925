import React, { useEffect, useState } from "react";
import { setBreadcrumbs, setPageTitle } from "@/store/common/commonSlice";
import { useDispatch, useSelector } from "react-redux";
import { useDeleteDataMutation, useFetchDataQuery, usePostDataMutation } from "@/store/api/apiSlice";
import usePagination from '@/hooks/paginationHook/usePagination';
import { Icon } from "@iconify/react";
import { useNavigate, Link } from "react-router-dom";
import { toast } from "react-toastify";
import useSingleData from '@/hooks/useSingleData';
import Card from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Modal from "@/components/ui/Modal";
import SingleStudentCreateForm from "@/components/teachersStudents/SingleStudentCreateForm";
import Table from "@/components/ui/table/Table";
import { replaceUploadUrl } from "@/helper/helperFunction";
import DefaultProfile from "@/assets/DefaultProfile.svg";
import SchoolList from "@/components/SchoolList";
import { fireResetPassword } from "@/components/ResetPassword";

const Students = () => {
  const dispatch = useDispatch();
  const [selectedSchool, setSelectedSchool] = useState("");
  const navigate = useNavigate();
  
  // Dynamically set the API URL based on selected school
  const jobListUrl = selectedSchool 
    ? `/get-student-list-by-school-id/${selectedSchool}` 
    : "/users";
    
  const {
    data: apiData,
    isLoading,
    currentPage,
    totalPages,
    handlePageChange, 
    error,
    handleReset,
    handleItemPerPage,
  } = usePagination(jobListUrl, useFetchDataQuery);
  const { fetchById, isLoading: isFetchingStudent } = useSingleData();

  const [deleteData] = useDeleteDataMutation();
  const [searchTerm, setSearchTerm] = useState("");
  
  // State for modals
  const [showAddModal, setShowAddModal] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState(null);

  const [postData] = usePostDataMutation();

  // Fetch countries for mapping country_id to name
  const { data: countriesData } = useFetchDataQuery("countries");
  const countryMap = React.useMemo(() => {
    if (!countriesData?.data) return {};
    const map = {};
    countriesData.data.forEach(country => {
      map[String(country.id)] = country.name;
    });
    return map;
  }, [countriesData]);

  // Fetch schools for mapping school_id to name
  const { data: schoolsData } = useFetchDataQuery("schools");
  const schoolMap = React.useMemo(() => {
    if (!schoolsData?.data?.data) return {};
    const map = {};
    schoolsData.data.data.forEach(school => {
      map[String(school.id)] = school.name;
    });
    return map;
  }, [schoolsData]);

  useEffect(() => {
    // Remove breadcrumb from topbar
    dispatch(setBreadcrumbs([]));
    
    dispatch(setPageTitle({
      isBackButton: false
    }));
  }, [dispatch]);

  // Handle school selection change
  const handleSchoolChange = (schoolId) => {
    setSelectedSchool(schoolId);
    if (currentPage !== 1) {
      handlePageChange(1);
    }
  };

  const deleteStudent = async (id) => {
    const confirmDelete = window.confirm("Are you sure you want to delete this student?");
    if (confirmDelete) {
      try {
        const res = await deleteData({ url: `/users/${id}` }).unwrap();
        if (res?.status === 200) {
          toast.success(res?.message);
          handleReset();
        } else {
          toast.error(res?.message || 'Failed to delete student');
        }
      } catch (error) {
        toast.error(error?.data?.message || 'Failed to delete student');
      }
    }
  }

  const openAddStudentModal = () => {
    setSelectedStudent(null);
    setShowAddModal(true);
  };
  
  const openUpdateStudentModal = async (id) => {
    try {
      const studentData = await fetchById('/users', id);
      if (studentData) {
        setSelectedStudent(studentData);
        setShowUpdateModal(true);
      }
    } catch (error) {
      console.error("Error fetching student data:", error);
      toast.error("Failed to fetch student data");
    }
  };

  // Filter data based on search term only (school filtering is now server-side)
  const filteredData = apiData?.data?.data
    ?.filter(student => student.user_type === "student")
    ?.filter(student => {
      const matchesSearch =
        student.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.name_jp?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.email?.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesSearch;
    }) || [];

  // Update resetPassword handler to directly fire SweetAlert
  const resetPassword = (studentId) => {
    fireResetPassword(studentId, postData, handleReset);
  };

  // Define table columns
  const columns = [
    {
      header: "#",
      accessorKey: "index",
      cell: ({ row }) => row.index + 1 + (currentPage - 1) * 10,
    },
    {
      header: "Profile",
      accessorKey: "profile_image",
      cell: ({ row }) => (
        <div className="h-10 w-10 rounded-full overflow-hidden flex-shrink-0">
          <img
            src={row.original.profile_image ? replaceUploadUrl(row.original.profile_image) : DefaultProfile}
            alt={row.original.name || "Profile"}
            className="h-full w-full object-cover"
            onError={e => {
              e.target.onerror = null;
              e.target.src = DefaultProfile;
            }}
          />
        </div>
      ),
    },
    {
      header: "Name",
      accessorKey: "name",
      cell: ({ row }) => (
        <div>
          <div className="text-sm font-medium text-gray-900">{row.original.name || "N/A"}</div>
          {row.original.name_jp && <div className="text-xs text-gray-500">{row.original.name_jp}</div>}
        </div>
      ),
    },
    { header: "Name JP", accessorKey: "name_jp", cell: ({ row }) => row.original.name_jp || "N/A" },
    { header: "Username", accessorKey: "username", cell: ({ row }) => row.original.username || "N/A" },
    { header: "Phone", accessorKey: "phone", cell: ({ row }) => row.original.phone || "N/A" },
    { header: "Email", accessorKey: "email", cell: ({ row }) => row.original.email || "N/A" },
    { header: "Age", accessorKey: "age", cell: ({ row }) => row.original.age || "N/A" },
    { header: "Gender", accessorKey: "gender", cell: ({ row }) => row.original.gender || "N/A" },
    { header: "School", accessorKey: "school_id", cell: ({ row }) => schoolMap[String(row.original.school_id)] || "N/A" },
    { header: "Grade ID", accessorKey: "grade_id", cell: ({ row }) => row.original.grade_id || "N/A" },
    { header: "Country", accessorKey: "country_id", cell: ({ row }) => countryMap[String(row.original.country_id)] || "N/A" },
    { header: "Hobby", accessorKey: "hobby", cell: ({ row }) => row.original.hobby || "N/A" },
    { header: "Favorite Movie", accessorKey: "favorite_movie", cell: ({ row }) => row.original.favorite_movie || "N/A" },
    { header: "Specialty", accessorKey: "specialty", cell: ({ row }) => row.original.specialty || "N/A" },
    { header: "User Type", accessorKey: "user_type", cell: ({ row }) => row.original.user_type || "N/A" },
    { header: "Favorite Added", accessorKey: "favorite_added", cell: ({ row }) => (row.original.favorite_added ? "Yes" : "No") },
    { header: "Active", accessorKey: "is_active", cell: ({ row }) => (
      row.original.is_active ? (
        <span className="inline-block px-2 py-1 text-xs font-semibold bg-green-100 text-green-700 rounded">Active</span>
      ) : (
        <span className="inline-block px-2 py-1 text-xs font-semibold bg-red-100 text-red-700 rounded">Inactive</span>
      )
    ) },
    {
      header: "Actions",
      id: "actions",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <button
            className="w-8 h-8 flex items-center justify-center rounded-md bg-white shadow border border-gray-100 hover:bg-gray-50"
            onClick={() => openUpdateStudentModal(row.original.id)}
          >
            <Icon icon="heroicons:pencil-square" className="text-slate-400" />
          </button>
          <button
            className="w-8 h-8 flex items-center justify-center rounded-md bg-white shadow border border-gray-100 hover:bg-gray-50"
            onClick={() => navigate(`/students/view/${row.original.id}`)}
          >
            <Icon icon="heroicons:eye" className="text-slate-400" />
          </button>
          <button
            className="w-8 h-8 flex items-center justify-center rounded-md bg-white shadow border border-gray-100 hover:bg-gray-50"
            onClick={() => resetPassword(row.original.id)}
          >
            <Icon icon="heroicons:key" className="text-slate-400" />
          </button>
        </div>
      ),
    },
  ];

  return (
    <div className="page-content page-min-height p-0">
      <div className="students-page">
        {/* Custom breadcrumb with reduced top padding */}
        <div className="flex items-center gap-2 pt-2 pb-4 mb-4">
          <Link to="/dashboard" className="text-gray-600 hover:text-primary-500">
            <Icon icon="heroicons-outline:home" className="text-lg" />
          </Link>
          <Icon icon="heroicons-outline:chevron-right" className="text-gray-400 text-sm" />
          <span className="text-orange-500 font-medium">Student List</span>
        </div>
        
        <Card className="border-0 shadow-none">
          <div className="mb-6">
            <div className="flex flex-wrap items-center justify-between gap-4">
              <h4 className="font-medium text-xl capitalize text-slate-900">
                Student List
              </h4>
              <div className="flex items-center gap-3">
                <div style={{ minWidth: 220 }}>
                  <SchoolList
                    value={selectedSchool}
                    onChange={opt => handleSchoolChange(opt ? opt.value : "")}
                    label=""
                  />
                </div>
                <div className="relative">
                  <input
                    type="text"
                    className="form-control py-2 pl-9 pr-4 rounded-md border border-slate-200 w-64 md:w-80"
                    placeholder="Search..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  <span className="absolute left-2 top-1/2 -translate-y-1/2 text-slate-400">
                    <Icon icon="heroicons-outline:search" />
                  </span>
                </div>
                <Button
                  className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 transition duration-300"
                  onClick={() => navigate("/students-bulk-upload")}
                >
                  Bulk Upload
                </Button>
                <Button
                  className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 transition duration-300"
                  onClick={openAddStudentModal}
                >
                  Add New Student
                </Button>
              </div>
            </div>
          </div>
          {/* Only show table if a school is selected */}
          {selectedSchool ? (
            <Table
              data={filteredData}
              columns={columns}
              currentPage={currentPage}
              handlePageChange={handlePageChange}
              paginationData={{
                currentPage,
                total: totalPages,
                perPage: 10,
              }}
            />
          ) : (
            <div className="flex flex-col items-center justify-center min-h-[60vh] py-16">
              <div className="bg-orange-50 shadow-lg rounded-2xl flex flex-col items-center justify-center max-w-2xl w-full py-20 px-10 border border-orange-100">
                <span className="mb-4">
                  <Icon icon="heroicons-outline:globe-alt" className="text-orange-400" width={48} height={48} />
                </span>
                <h2 className="text-2xl font-bold text-orange-700 mb-2 text-center">
                  Please select a school
                </h2>
                <p className="text-gray-600 text-base text-center max-w-md">
                  Use the dropdown above to view the student list for a specific school.
                </p>
              </div>
            </div>
          )}
        </Card>
      </div>
      
      {/* Add Student Modal */}
      <Modal
        activeModal={showAddModal}
        onClose={() => setShowAddModal(false)}
        title="Add New Student"
        centered
        scrollContent
        className="max-w-3xl"
        themeClass="bg-orange-400 dark:bg-slate-800"
      >
        <div className="p-4">
          <SingleStudentCreateForm 
            onSuccess={() => {
              setShowAddModal(false);
              handleReset();
            }}
          />
        </div>
      </Modal>
      
      {/* Update Student Modal */}
      <Modal
        activeModal={showUpdateModal}
        onClose={() => setShowUpdateModal(false)}
        title="Update Student"
        centered
        scrollContent
        className="max-w-3xl"
        themeClass="bg-orange-100 dark:bg-slate-800"
      >
        <div className="p-4">
          <SingleStudentCreateForm 
            studentData={selectedStudent}
            isUpdateMode={true}
            onSuccess={() => {
              setShowUpdateModal(false);
              handleReset();
            }}
          />
        </div>
      </Modal>
    </div>
  );
};

export default Students;
