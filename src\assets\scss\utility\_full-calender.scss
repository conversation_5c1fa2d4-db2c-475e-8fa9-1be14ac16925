.dashcode-app {
  .fc-toolbar-chunk button {
    height: 50px;
    //min-width: 70px;
    &.fc-prev-button {
      &:after {
        // content: url("https://api.iconify.design/akar-icons/chevron-left.svg?color=white&width=24");
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
    &.fc-next-button {
      &:after {
        //content: url("https://api.iconify.design/akar-icons/chevron-right.svg?color=white&width=24");
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
  .fc-button {
    font-size: 14px !important;
    line-height: 14px !important;
    height: auto !important;
    text-transform: capitalize !important;
    font-family: Inter !important;
    padding: 12px 20px 12px 20px !important;
  }
  .fc .fc-button-primary {
    background: transparent !important;
    @apply text-slate-900 dark:text-white border-slate-100;
  }
  .fc .fc-button-primary:not(:disabled):active,
  .fc .fc-button-primary:not(:disabled).fc-button-active,
  .fc .fc-button-primary:hover {
    background: #111112 !important;
    color: #fff !important;
  }

  .fc .fc-button-primary:disabled {
    background: #a0aec0 !important;
    border-color: #a0aec0 !important;
    @apply cursor-not-allowed;
  }

  .fc .fc-daygrid-day.fc-day-today {
    background: rgba(95, 99, 242, 0.04) !important;
  }

  .fc .fc-button-primary:focus {
    box-shadow: none !important;
  }
  .fc-theme-standard .fc-scrollgrid {
    border-color: #eef1f9 !important;
  }

  .fc-theme-standard td,
  .fc-theme-standard th {
    @apply border-slate-100 dark:border-slate-700;
  }
  .fc-col-header-cell .fc-scrollgrid-sync-inner {
    @apply bg-slate-50 dark:bg-slate-700  text-xs text-slate-500 dark:text-slate-300 font-normal py-3;
  }
  .fc-daygrid-day-top {
    @apply text-sm px-3 py-2  text-slate-900 dark:text-white font-normal;
  }
  .fc-h-event .fc-event-main-frame {
    @apply justify-center text-center w-max mx-auto;
    .fc-event-time {
      @apply font-normal flex-none;
    }
  }
  .fc-event-time {
    @apply text-sm font-normal;
  }
  .fc-event-title {
    font-size: 14px !important;
    font-weight: 300 !important;
  }
  .fc .fc-toolbar-title {
    @apply text-lg font-normal text-slate-600 dark:text-slate-300;
  }
  // event css
  .fc-daygrid-event-dot {
    @apply hidden;
  }

  @media (max-width: 981px) {
    .fc-button-group,
    .fc .fc-toolbar {
      display: block !important;
    }
    .fc .fc-toolbar {
      @apply space-y-4;
    }
    .fc-toolbar-chunk {
      @apply space-y-4;
    }
    .fc .fc-button {
      padding: 0.4em 0.65em !important;
    }
  }
  .fc .fc-timegrid-axis-cushion,
  .fc .fc-timegrid-slot-label-cushion {
    @apply dark:text-slate-300;
  }
  .fc .fc-list-event:hover td {
    @apply bg-inherit;
  }
  .fc .fc-list-event-dot {
    @apply hidden;
  }
  .fc-direction-ltr .fc-list-day-text,
  .fc-direction-rtl .fc-list-day-side-text,
  .fc-direction-ltr .fc-list-day-side-text,
  .fc-direction-rtl .fc-list-day-text {
    font-size: 16px;
    font-weight: 500;
  }
}

//

.dark {
  .fc-col-header-cell .fc-scrollgrid-sync-inner {
    @apply bg-slate-700 text-slate-300;
  }
  .fc-daygrid-day-top {
    @apply text-slate-300;
  }
  .fc .fc-day-other .fc-daygrid-day-top {
    @apply opacity-70;
  }
  .fc .fc-button-primary {
    @apply border-slate-600 text-slate-300;
  }
  .fc-theme-standard td,
  .fc-theme-standard th {
    @apply border-slate-700;
  }
  .fc .fc-toolbar-title {
    @apply text-slate-300;
  }
  .fc .fc-button-primary:not(:disabled):active,
  .fc .fc-button-primary:not(:disabled).fc-button-active,
  .fc .fc-button-primary:hover {
    background: #0f172a !important;
  }

  .fc .fc-button-primary:disabled {
    background: #334155 !important;
    border-color: #334155 !important;
  }

  .fc .fc-daygrid-day.fc-day-today {
    background: #334155 !important;
  }

  .fc-theme-standard .fc-scrollgrid {
    border-color: #334155 !important;
  }
}
.premium-calendar {
  // Premium calendar styling with advanced visual effects
  .fc {
    font-family: 'Inter', sans-serif;
    
    // Hide default buttons since we have custom view selector
    .fc-button-group {
      @apply hidden;
    }
    
    // Elegant toolbar
    .fc-toolbar {
      @apply px-6 py-4 mb-6 flex items-center;
      
      .fc-toolbar-title {
        @apply text-2xl font-bold text-slate-800 dark:text-white tracking-tight;
        background: linear-gradient(135deg, #F1841B, #D3750A);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      
      .fc-prev-button, .fc-next-button {
        @apply bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 border-0 rounded-full h-9 w-9 flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-200;
        
        &:hover {
          @apply bg-primary-50 dark:bg-primary-900/20 text-primary-500 dark:text-primary-400;
        }
        
        .fc-icon {
          @apply text-base;
        }
      }
      
      .fc-today-button {
        @apply bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-300 border border-slate-200 dark:border-slate-600 rounded-full px-4 py-1.5 text-sm font-medium shadow-sm hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:text-primary-500 dark:hover:text-primary-400 transition-all duration-200;
      }
    }
    
    // Elegant header
    .fc-col-header {
      @apply mb-2;
      
      .fc-col-header-cell {
        .fc-scrollgrid-sync-inner {
          @apply py-3;
          
          .fc-col-header-cell-cushion {
            @apply text-sm font-semibold text-slate-600 dark:text-slate-400 uppercase tracking-wider;
          }
        }
      }
    }
    
    // Elegant day cells
    .fc-daygrid-day-frame {
      @apply p-1 transition-all duration-200 rounded-lg;
      
      .fc-daygrid-day-top {
        @apply justify-center pt-1;
        
        .fc-daygrid-day-number {
          @apply text-sm font-medium text-slate-600 dark:text-slate-400 rounded-full w-8 h-8 flex items-center justify-center transition-all duration-200;
        }
      }
      
      &:hover {
        @apply bg-slate-50 dark:bg-slate-700/30;
        
        .fc-daygrid-day-number {
          @apply bg-white dark:bg-slate-700 shadow-sm;
        }
      }
    }
    
    // Today highlight
    .fc-day-today {
      background: transparent !important;
      
      .fc-daygrid-day-frame {
        @apply bg-primary-50 dark:bg-primary-900/10;
        
        &::after {
          content: '';
          @apply absolute inset-0 border-2 border-primary-500/30 dark:border-primary-500/20 rounded-lg pointer-events-none;
        }
      }
      
      .fc-daygrid-day-number {
        @apply bg-primary-500 text-white font-semibold shadow-md;
      }
    }
    
    // Elegant events
    .fc-h-event {
      @apply rounded-lg border-none shadow-md transition-all duration-200 overflow-hidden;
      background: linear-gradient(135deg, #F1841B, #D3750A) !important;
      
      &::before {
        content: '';
        @apply absolute inset-0 bg-white opacity-0 transition-opacity duration-200;
      }
      
      &:hover {
        transform: translateY(-2px) scale(1.02);
        @apply shadow-lg;
        
        &::before {
          @apply opacity-10;
        }
      }
      
      .fc-event-main {
        @apply px-3 py-1.5;
      }
      
      .fc-event-title {
        @apply font-medium;
      }
      
      .fc-event-time {
        @apply font-semibold opacity-90;
      }
    }
    
    // Other month days
    .fc-day-other {
      .fc-daygrid-day-top {
        .fc-daygrid-day-number {
          @apply text-slate-400 dark:text-slate-600 font-normal opacity-60;
        }
      }
      
      .fc-daygrid-day-frame {
        @apply bg-slate-50/50 dark:bg-slate-800/30;
      }
    }
    
    // Grid styling
    .fc-scrollgrid {
      @apply border-0 rounded-xl overflow-hidden;
      
      td, th {
        @apply border-slate-100 dark:border-slate-700;
      }
    }
    
    // Time grid
    .fc-timegrid-slot {
      @apply h-14;
    }
    
    .fc-timegrid-now-indicator-line {
      @apply border-primary-500 border-2;
    }
    
    .fc-timegrid-now-indicator-arrow {
      @apply border-primary-500;
    }
    
    // More link
    .fc-daygrid-more-link {
      @apply text-xs font-medium text-primary-500 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 px-2 py-0.5 rounded-full shadow-sm hover:shadow transition-all duration-200;
    }
    
    // Week numbers
    .fc-daygrid-week-number {
      @apply text-xs font-medium text-slate-400 dark:text-slate-500 bg-slate-50 dark:bg-slate-700/30 rounded-full px-2 py-0.5;
    }
    
    // List view
    .fc-list {
      @apply rounded-xl overflow-hidden border-0 shadow-sm;
      
      .fc-list-day-cushion {
        @apply bg-slate-50 dark:bg-slate-700/50;
      }
      
      .fc-list-event {
        @apply hover:bg-primary-50 dark:hover:bg-primary-900/10 cursor-pointer transition-colors duration-200;
        
        td {
          @apply border-slate-100 dark:border-slate-700;
        }
        
        .fc-list-event-dot {
          @apply border-primary-500;
        }
        
        .fc-list-event-title {
          @apply font-medium text-slate-700 dark:text-slate-300;
        }
        
        .fc-list-event-time {
          @apply text-slate-500 dark:text-slate-400 font-medium;
        }
      }
    }
    
    // Popover
    .fc-popover {
      @apply border-0 shadow-xl rounded-lg overflow-hidden;
      
      .fc-popover-header {
        @apply bg-white dark:bg-slate-700 px-3 py-2;
        
        .fc-popover-title {
          @apply text-sm font-medium text-slate-700 dark:text-slate-300;
        }
        
        .fc-popover-close {
          @apply text-slate-400 hover:text-slate-700 dark:hover:text-slate-200;
        }
      }
      
      .fc-popover-body {
        @apply p-2 bg-white dark:bg-slate-800;
      }
    }
  }
  
  // Event types with gradient backgrounds and animations
  .event-class {
    background: linear-gradient(135deg, #4669FA, #3A5BD1) !important;
    animation: shimmer 2s infinite linear;
  }
  
  .event-exam {
    background: linear-gradient(135deg, #F1841B, #D3750A) !important;
    animation: shimmer 2s infinite linear;
  }
  
  .event-meeting {
    background: linear-gradient(135deg, #50C793, #3F9A7A) !important;
    animation: shimmer 2s infinite linear;
  }
  
  .event-holiday {
    background: linear-gradient(135deg, #FA916B, #DF8260) !important;
    animation: shimmer 2s infinite linear;
  }
  
  @keyframes shimmer {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
  
  // Custom view selector
  .calendar-view-selector {
    button {
      @apply transition-all duration-200;
      
      &:hover {
        @apply text-primary-500;
      }
    }
  }
}
.ultra-calendar {
  // Ultra-modern calendar styling with advanced visual effects
  
  // Custom header styling
  .calendar-custom-header {
    @apply bg-white dark:bg-slate-800;
    
    .current-date-display h3 {
      background: linear-gradient(135deg, #F1841B, #D3750A);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      @apply min-w-[180px] text-center;
    }
    
    .nav-btn {
      @apply transition-transform duration-200;
      
      &:hover {
        transform: scale(1.05);
      }
      
      &:active {
        transform: scale(0.95);
      }
    }
  }
  
  // Event categories legend
  .event-categories-legend {
    .event-category-item {
      @apply transition-transform duration-200 hover:scale-105 cursor-pointer px-2 py-1 rounded-full hover:bg-white dark:hover:bg-slate-700 shadow-sm;
    }
  }
  
  // Calendar container
  .calendar-container {
    @apply bg-white dark:bg-slate-800;
  }
  
  // Main calendar styling
  .fc {
    font-family: 'Inter', sans-serif;
    
    // Hide default header since we have a custom one
    .fc-header-toolbar {
      @apply hidden;
    }
    
    // Month view - day cells
    .fc-daygrid-day-frame {
      @apply p-1 transition-all duration-200 rounded-lg relative overflow-hidden;
      
      // Add subtle pattern to day cells
      &::before {
        content: '';
        @apply absolute inset-0 opacity-5 dark:opacity-10 pointer-events-none;
        background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='1' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='1'/%3E%3C/g%3E%3C/svg%3E");
      }
      
      .fc-daygrid-day-top {
        @apply justify-end pt-1 pr-2;
        
        .fc-daygrid-day-number {
          @apply text-sm font-medium text-slate-600 dark:text-slate-400 rounded-full w-7 h-7 flex items-center justify-center transition-all duration-200;
        }
      }
      
      &:hover {
        @apply bg-slate-50 dark:bg-slate-700/30;
        
        .fc-daygrid-day-number {
          @apply bg-white dark:bg-slate-700 shadow-sm;
        }
      }
    }
    
    // Today highlight with special design
    .fc-day-today {
      background: transparent !important;
      
      .fc-daygrid-day-frame {
        @apply bg-primary-50 dark:bg-primary-900/10;
        
        &::after {
          content: '';
          @apply absolute inset-0 border-2 border-primary-500/30 dark:border-primary-500/20 rounded-lg pointer-events-none;
        }
        
        // Add subtle animated gradient to today's cell
        &::before {
          content: '';
          @apply absolute inset-0 opacity-10 dark:opacity-5 pointer-events-none;
          background: linear-gradient(135deg, rgba(241, 132, 27, 0.3), rgba(211, 117, 10, 0.3));
          animation: todayGradient 3s infinite alternate;
        }
      }
      
      .fc-daygrid-day-number {
        @apply bg-primary-500 text-white font-semibold shadow-md;
      }
    }
    
    // Event styling for different categories
    .fc-event {
      @apply rounded-lg border-none shadow-md transition-all duration-200 overflow-hidden;
      
      // Add subtle shine effect
      &::before {
        content: '';
        @apply absolute inset-0 bg-white opacity-0 transition-opacity duration-200;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transform: translateX(-100%);
      }
      
      &:hover {
        transform: translateY(-2px) scale(1.02);
        @apply shadow-lg;
        
        &::before {
          animation: shine 1.5s infinite;
        }
      }
      
      .fc-event-main {
        @apply px-3 py-1.5;
      }
      
      .fc-event-title {
        @apply font-medium;
      }
      
      .fc-event-time {
        @apply font-semibold opacity-90;
      }
    }
    
    // Other month days with faded appearance
    .fc-day-other {
      .fc-daygrid-day-top {
        .fc-daygrid-day-number {
          @apply text-slate-400 dark:text-slate-600 font-normal opacity-60;
        }
      }
      
      .fc-daygrid-day-frame {
        @apply bg-slate-50/50 dark:bg-slate-800/30;
      }
    }
    
    // Grid styling with subtle borders
    .fc-scrollgrid {
      @apply border-0 rounded-xl overflow-hidden;
      
      td, th {
        @apply border-slate-100 dark:border-slate-700;
      }
    }
    
    // Column headers with gradient background
    .fc-col-header-cell {
      .fc-scrollgrid-sync-inner {
        @apply py-3 relative overflow-hidden;
        background: linear-gradient(to bottom, rgba(241, 132, 27, 0.05), transparent) !important;
        
        .fc-col-header-cell-cushion {
          @apply text-sm font-semibold text-slate-600 dark:text-slate-400 uppercase tracking-wider;
        }
      }
    }
    
    // Time grid with improved styling
    .fc-timegrid-slot {
      @apply h-14;
    }
    
    .fc-timegrid-now-indicator-line {
      @apply border-primary-500 border-2;
    }
    
    .fc-timegrid-now-indicator-arrow {
      @apply border-primary-500;
    }
    
    // More link with badge style
    .fc-daygrid-more-link {
      @apply text-xs font-medium text-primary-500 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20 px-2 py-0.5 rounded-full shadow-sm hover:shadow transition-all duration-200;
    }
    
    // Week numbers with pill style
    .fc-daygrid-week-number {
      @apply text-xs font-medium text-slate-400 dark:text-slate-500 bg-slate-50 dark:bg-slate-700/30 rounded-full px-2 py-0.5;
    }
    
    // List view with modern styling
    .fc-list {
      @apply rounded-xl overflow-hidden border-0 shadow-sm;
      
      .fc-list-day-cushion {
        @apply bg-slate-50 dark:bg-slate-700/50;
        background: linear-gradient(to right, rgba(241, 132, 27, 0.05), transparent) !important;
      }
      
      .fc-list-event {
        @apply hover:bg-primary-50 dark:hover:bg-primary-900/10 cursor-pointer transition-colors duration-200;
        
        td {
          @apply border-slate-100 dark:border-slate-700;
        }
        
        .fc-list-event-dot {
          @apply border-primary-500;
        }
        
        .fc-list-event-title {
          @apply font-medium text-slate-700 dark:text-slate-300;
        }
        
        .fc-list-event-time {
          @apply text-slate-500 dark:text-slate-400 font-medium;
        }
      }
    }
  }
  
  // Event types with custom styling
  .event-class {
    background: linear-gradient(135deg, #4669FA, #3A5BD1) !important;
  }
  
  .event-exam {
    background: linear-gradient(135deg, #F1841B, #D3750A) !important;
  }
  
  .event-meeting {
    background: linear-gradient(135deg, #50C793, #3F9A7A) !important;
  }
  
  .event-holiday {
    background: linear-gradient(135deg, #FA916B, #DF8260) !important;
  }
  
  // Animations
  @keyframes shine {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }
  
  @keyframes todayGradient {
    0% {
      opacity: 0.05;
    }
    100% {
      opacity: 0.15;
    }
  }
}
