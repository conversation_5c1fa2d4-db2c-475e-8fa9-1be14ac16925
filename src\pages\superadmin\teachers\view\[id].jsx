import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { Icon } from "@iconify/react";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { setPageTitle } from "@/store/common/commonSlice";
import useSingleData from "@/hooks/useSingleData";
import DefaultProfile from "@/assets/DefaultProfile.svg";
import { replaceUploadUrl } from "@/helper/helperFunction";
import Modal from "@/components/ui/Modal";
import TeacherForm from "@/components/teachersStudents/TeacherForm";

export default function TeacherProfile() {
  const { id } = useParams();
  const dispatch = useDispatch();
  const { fetchById, isLoading } = useSingleData();
  const [teacherData, setTeacherData] = useState(null);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState('Week');
  const [events, setEvents] = useState([
    { id: 1, day: 0, hour: 11, duration: 3, title: "Math Class", student: "<PERSON> Doe" },
    { id: 2, day: 2, hour: 13, duration: 2, title: "Science Lab", student: "Jane Smith" },
    { id: 3, day: 5, hour: 14, duration: 3, title: "English Tutoring", student: "Mike Johnson" }
  ]);
  const [showModal, setShowModal] = useState(false);
  const [selectedSlot, setSelectedSlot] = useState(null);
  const [formData, setFormData] = useState({ title: '', student: '', duration: 1 });
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);

  useEffect(() => {
    dispatch(setPageTitle({
      title: "Teacher Profile",
      isBackButton: true
    }));
    
    const loadTeacher = async () => {
      try {
        const data = await fetchById('/users', id);
        setTeacherData(data);
      } catch (error) {
        console.error("Error fetching teacher:", error);
      }
    };
    
    loadTeacher();
  }, [id, dispatch, fetchById]);

  const navigateWeek = (direction) => {
    const newDate = new Date(currentDate);
    newDate.setDate(currentDate.getDate() + (direction * 7));
    setCurrentDate(newDate);
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  const formatWeekRange = (date) => {
    const startOfWeek = new Date(date);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1);
    startOfWeek.setDate(diff);
    
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    
    const options = { month: 'short', day: 'numeric' };
    return `${startOfWeek.toLocaleDateString('en-US', options)} – ${endOfWeek.toLocaleDateString('en-US', options)}, ${date.getFullYear()}`;
  };

  const getDaysOfWeek = (date) => {
    const startOfWeek = new Date(date);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1);
    startOfWeek.setDate(diff);
    
    const days = [];
    for (let i = 0; i < 7; i++) {
      const currentDay = new Date(startOfWeek);
      currentDay.setDate(startOfWeek.getDate() + i);
      days.push(currentDay);
    }
    return days;
  };

  const weekDays = getDaysOfWeek(currentDate);
  const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  const handleEditProfile = () => {
    setShowEditModal(true);
  };

  const handleEditSuccess = () => {
    setShowEditModal(false);
    // Reload teacher data
    const loadTeacher = async () => {
      try {
        const data = await fetchById('/users', id);
        setTeacherData(data);
      } catch (error) {
        console.error("Error fetching teacher:", error);
      }
    };
    loadTeacher();
  };

  if (isLoading || !teacherData) {
    return <div className="flex justify-center items-center h-96">Loading...</div>;
  }

  return (
    <div className="p-6 bg-[#F5F6F8] min-h-screen">
      <div className="flex items-center text-sm text-gray-400 mb-6">
        <Link to="/" className="text-gray-500 hover:text-orange-500">
          <Icon icon="heroicons-outline:home" className="text-lg" />
        </Link>
        <Icon icon="heroicons-outline:chevron-right" className="mx-2 text-gray-300" />
        <Link to="/all-teachers" className="text-gray-500 hover:text-orange-500">
          Teacher List
        </Link>
        <Icon icon="heroicons-outline:chevron-right" className="mx-2 text-gray-300" />
        <span className="text-orange-500 font-medium">Teacher Profile</span>
      </div>

        <div className=" rounded-xl p-6 mb-8 shadow-sm bg-white relative border ">

      <div className="bg-[#FFF3E6] rounded-xl p-4 mb-8 shadow flex items-center relative border border-[#FFE0B2]">
        <div className="flex-shrink-0">
          <div className="bg-[#FFD9B3] p-2 rounded-xl w-[136px] h-[150px] flex items-center justify-center">
            {teacherData.profile_image ? (
              <img
                src={replaceUploadUrl(teacherData.profile_image)}
                alt={teacherData.name}
                className="w-[100px] h-[100px] rounded-full object-cover border-2 border-[#FF9800]"
                onError={(e) => {
                  e.target.src = DefaultProfile;
                }}
              />
            ) : (
              <img
                src={DefaultProfile}
                alt={teacherData.name}
                className="w-[100px] h-[100px] rounded-full object-cover border-2 border-[#FF9800]"
              />
            )}
          </div>
        </div>
        <div className="flex-1 flex flex-row items-start px-6">
          <div className="flex-1 grid grid-cols-[120px_1fr] gap-y-2 items-center text-[15px]">
            <span className="font-semibold text-gray-800">Name</span>
            <span className="text-gray-900">:   {teacherData.name || 'N/A'}</span>
            
            <span className="font-semibold text-gray-800">Japanese Name</span>
            <span className="text-gray-900">:   {teacherData.name_jp || 'N/A'}</span>
            
            <span className="font-semibold text-gray-800">Age</span>
            <span className="text-gray-900">:   {teacherData.age || 'N/A'}</span>
            
            <span className="font-semibold text-gray-800">Gender</span>
            <span className="text-gray-900">:   {teacherData.gender || 'N/A'}</span>
            
            <span className="font-semibold text-gray-800">Email Address</span>
            <span className="text-gray-900">:   {teacherData.email || 'N/A'}</span>
          </div>
          <div className="h-[150px] w-[3px] bg-[#FFD9B3] mx-7" />
          <div className="flex-1 grid grid-cols-[120px_1fr] gap-y-2 items-center text-[15px]">
            <span className="font-semibold text-gray-800">Phone</span>
            <span className="text-gray-900">:   {teacherData.phone || 'N/A'}</span>
            
            <span className="font-semibold text-gray-800">Username</span>
            <span className="text-gray-900">:   {teacherData.username || 'N/A'}</span>
            
            {/* <span className="font-semibold text-gray-800">Hobby</span>
            <span className="text-gray-900">:   {teacherData.hobby || 'N/A'}</span> */}
            
            <span className="font-semibold text-gray-800">Favorite Movie</span>
            <span className="text-gray-900">:   {teacherData.favorite_movie || 'N/A'}</span>
            
            <span className="font-semibold text-gray-800">Specialty</span>
            <span className="text-gray-900">:   {teacherData.specialty || 'N/A'}</span>
          </div>
        </div>
        <button 
          className="absolute top-4 right-4 bg-orange-500 text-white px-4 py-1.5 rounded font-medium hover:bg-orange-600 text-sm shadow flex items-center gap-2"
          onClick={handleEditProfile}
        >
          <Icon icon="heroicons:pencil-square" className="text-lg" />
          Edit Profile
        </button>
      </div>

      <div>
        <h2 className="font-semibold mb-4 text-gray-700" style={{ fontSize: "1.875rem" }}>
          Teachers Calendar
        </h2>      
        <div className="flex items-center justify-between mb-4 w-full">
          <div>
            <button 
              onClick={goToToday}
              className="px-6 py-2 rounded-full bg-white shadow text-[#64748B] font-semibold border border-[#F1F5F9] text-base hover:bg-[#F1F5F9] hover:text-[#2563EB] hover:border-[#2563EB]"
            >
              <span className="font-bold">Today</span>
            </button>
          </div>
          <div className="flex items-center gap-4">
            <button 
              onClick={() => navigateWeek(-1)}
              className="w-9 h-9 flex items-center justify-center rounded-full bg-white border border-[#F1F5F9] text-[#94A3B8] shadow hover:bg-[#F1F5F9] hover:text-[#2563EB]"
            >
              <Icon icon="heroicons-outline:chevron-left" className="text-lg" />
            </button>
            <span className="font-semibold text-[#64748B] text-lg">{formatWeekRange(currentDate)}</span>
            <button 
              onClick={() => navigateWeek(1)}
              className="w-9 h-9 flex items-center justify-center rounded-full bg-white border border-[#F1F5F9] text-[#94A3B8] shadow hover:bg-[#F1F5F9] hover:text-[#2563EB]"
            >
              <Icon icon="heroicons-outline:chevron-right" className="text-lg" />
            </button>
          </div>
          <div className="flex bg-white rounded-full border border-[#F1F5F9] shadow overflow-hidden">
            {['Year', 'Week', 'Month', 'Day'].map((mode) => (
              <button 
                key={mode}
                onClick={() => setViewMode(mode)}
                className={`px-6 py-2 font-semibold text-base ${
                  viewMode === mode 
                    ? 'text-[#2563EB] bg-[#F1F5F9]' 
                    : 'text-[#94A3B8] hover:bg-[#F1F5F9] hover:text-[#2563EB]'
                }`}
              >
                {mode}
              </button>
            ))}
          </div>
        </div>
        <div className="bg-white rounded-xl shadow p-4 overflow-x-auto">
          <table className="min-w-full text-center border-separate" style={{ borderSpacing: 0 }}>
            <thead>
              <tr>
                <th className="w-12 bg-white align-middle border border-[#F1F5F9]">
                  <span className="flex justify-center items-center h-full">
                    <Icon icon="heroicons-outline:clock" className="text-[#CBD5E1] text-lg" />
                  </span>
                </th>
                {weekDays.map((day, idx) => (
                  <th
                    key={idx}
                    className="text-xs text-[#64748B] font-semibold py-2 px-4 bg-white border border-[#F1F5F9]"
                  >
                    {dayNames[idx]} {day.getDate()}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {[...Array(10)].map((_, rowIdx) => {
                const hour = 9 + rowIdx;
                return (
                  <tr key={hour}>
                    <td className="text-xs text-[#94A3B8] font-semibold py-2 px-2 bg-white sticky left-0 z-10 border border-[#F1F5F9]">
                      {hour < 10 ? `0${hour}:00` : `${hour}:00`}
                    </td>
                    {[...Array(7)].map((_, colIdx) => {
                      const event = events.find(e => e.day === colIdx && e.hour === hour);
                      const hasEventBlock = events.some(e => 
                        e.day === colIdx && 
                        hour >= e.hour && 
                        hour < e.hour + e.duration
                      );

                      return (
                        <td
                          key={colIdx}
                          className={`py-2 align-top min-w-[120px] h-[60px] border border-[#F1F5F9] cursor-pointer hover:bg-[#F8F9FA] ${
                            hasEventBlock ? "bg-[#F8F8FC]" : "bg-white"
                          }`}
                          onClick={() => {
                            if (!event) {
                              setSelectedSlot({ day: colIdx, hour: hour });
                              setShowModal(true);
                            } else {
                              setSelectedEvent(event);
                              setShowDetailsModal(true);
                            }
                          }}
                        >
                          {event && event.hour === hour && (
                            <div className="bg-[#2563EB] text-white text-xs p-2 rounded m-1 shadow">
                              <div className="font-semibold">{event.title}</div>
                              <div className="text-[#E2E8F0]">{event.student}</div>
                            </div>
                          )}
                        </td>
                      );
                    })}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
      </div>

      {/* Calendar Event Modals */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 shadow-xl">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-800">Create New Event</h3>
              <button 
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <Icon icon="heroicons:x-mark" className="text-xl" />
              </button>
            </div>
            
            <form onSubmit={(e) => {
              e.preventDefault();
              const newEvent = {
                id: Date.now(),
                day: selectedSlot.day,
                hour: selectedSlot.hour,
                duration: parseInt(formData.duration),
                title: formData.title,
                student: formData.student
              };
              setEvents([...events, newEvent]);
              setShowModal(false);
            }}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Event Title</label>
                  <input
                    type="text"
                    required
                    value={formData.title}
                    onChange={(e) => setFormData({...formData, title: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                    placeholder="Enter event title"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Student Name</label>
                  <input
                    type="text"
                    required
                    value={formData.student}
                    onChange={(e) => setFormData({...formData, student: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                    placeholder="Enter student name"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Duration (hours)</label>
                  <select
                    value={formData.duration}
                    onChange={(e) => setFormData({...formData, duration: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                  >
                    {[1,2,3,4,5].map(hour => (
                      <option key={hour} value={hour}>{hour} hour{hour > 1 ? 's' : ''}</option>
                    ))}
                  </select>
                </div>
                
                <div className="text-sm text-gray-600">
                  <strong>Time:</strong> {selectedSlot ? `${dayNames[selectedSlot.day]} ${selectedSlot.hour < 10 ? `0${selectedSlot.hour}` : selectedSlot.hour}:00` : ''}
                </div>
              </div>
              
              <div className="flex gap-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex-1 px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600"
                >
                  Create Event
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {showDetailsModal && selectedEvent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 shadow-xl">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-800">Event Details</h3>
              <button 
                onClick={() => setShowDetailsModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <Icon icon="heroicons:x-mark" className="text-xl" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center gap-4 mb-4">
                {teacherData.profile_image ? (
                  <img src={replaceUploadUrl(teacherData.profile_image)} alt="Teacher" className="w-16 h-16 rounded-full border-2 border-orange-500" />
                ) : (
                  <img src={DefaultProfile} alt="Teacher" className="w-16 h-16 rounded-full border-2 border-orange-500" />
                )}
                <div>
                  <h4 className="font-semibold text-gray-800">{teacherData.name}</h4>
                  <p className="text-sm text-gray-600">Teacher</p>
                </div>
              </div>
              
              <div className="bg-orange-50 rounded-lg p-4 space-y-3">
                <div className="flex justify-between">
                  <span className="font-medium text-gray-700">Event Title:</span>
                  <span className="text-gray-900">{selectedEvent.title}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="font-medium text-gray-700">Student:</span>
                  <span className="text-gray-900">{selectedEvent.student}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="font-medium text-gray-700">Date:</span>
                  <span className="text-gray-900">{dayNames[selectedEvent.day]} {weekDays[selectedEvent.day]?.getDate()}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="font-medium text-gray-700">Time:</span>
                  <span className="text-gray-900">
                    {selectedEvent.hour < 10 ? `0${selectedEvent.hour}` : selectedEvent.hour}:00 - 
                    {selectedEvent.hour + selectedEvent.duration < 10 ? `0${selectedEvent.hour + selectedEvent.duration}` : selectedEvent.hour + selectedEvent.duration}:00
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="font-medium text-gray-700">Duration:</span>
                  <span className="text-gray-900">{selectedEvent.duration} hour{selectedEvent.duration > 1 ? 's' : ''}</span>
                </div>
              </div>
              
              <div className="flex gap-3 mt-6">
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Close
                </button>
                <button
                  onClick={() => {
                    setEvents(events.filter(ev => ev.id !== selectedEvent.id));
                    setShowDetailsModal(false);
                  }}
                  className="flex-1 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
                >
                  Delete Event
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Teacher Modal */}
      <Modal
        title="Edit Teacher Profile"
        activeModal={showEditModal}
        onClose={() => setShowEditModal(false)}
        centered
        className="max-w-4xl"
      >
        <TeacherForm 
          teacherData={teacherData}
          isUpdateMode={true}
          onSuccess={handleEditSuccess}
        />
      </Modal>
    </div>
  );
}