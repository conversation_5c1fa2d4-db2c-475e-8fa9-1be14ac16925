import React from 'react';

const TableSkeleton = ({ rows = 10, columns = 5 }) => {
    return (
        <div className="w-full border border-gray-200 rounded-md overflow-hidden">
            <div className="bg-gray-100 dark:bg-slate-800 py-3 px-4">
                <div className="flex gap-4">
                    {[...Array(columns)].map((_, colIndex) => (
                        <div key={colIndex} className="flex-1 h-4 bg-gray-300 dark:bg-slate-700 rounded"></div>
                    ))}
                </div>
            </div>
            <div className="divide-y divide-gray-200 dark:divide-slate-700">
                {[...Array(rows)].map((_, rowIndex) => (
                    <div key={rowIndex} className="flex gap-4 items-center py-4 px-4">
                        {[...Array(columns)].map((_, colIndex) => (
                            <div key={colIndex} className="flex-1 h-4 bg-gray-200 dark:bg-slate-600 rounded animate-pulse"></div>
                        ))}
                    </div>
                ))}
            </div>
        </div>
    );
};

export default TableSkeleton;
