import { apiSlice } from "../apiSlice";

export const calendarApi = apiSlice.injectEndpoints({
  tagTypes: ["events", "eventTypes"],
  endpoints: (builder) => ({
    getCategories: builder.query({
      query: () => "/categories",
    }),
    getEventTypes: builder.query({
      query: () => "/event-types",
      providesTags: ["eventTypes"],
    }),
    getCalendarEvents: builder.query({
      query: () => "/calendar-events",
      providesTags: ["events"],
    }),
    getEventType: builder.query({
      query: (id) => `/event-types/${id}`,
      providesTags: (result, error, id) => [{ type: "eventTypes", id }],
    }),
    createEventType: builder.mutation({
      query: (eventType) => ({
        url: "/event-types",
        method: "POST",
        body: eventType,
        headers: {
          'Content-Type': 'application/json',
        },
      }),
      invalidatesTags: ["eventTypes"],
    }),
    updateEventType: builder.mutation({
      query: ({ id, ...eventType }) => ({
        url: `/event-types/${id}`,
        method: "PUT",
        body: eventType,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "eventTypes", id },
        "eventTypes",
      ],
    }),
    createCalendarEvent: builder.mutation({
      query: (event) => ({
        url: "/calendar-events",
        method: "POST",
        body: event,
        headers: {
          'Content-Type': 'application/json',
        },
      }),
      invalidatesTags: ["events"],
    }),
    editCalendarEvent: builder.mutation({
      query: ({ id, event }) => ({
        url: `/calendar-events/${id}`,
        method: "PUT",
        body: event,
        headers: {
          'Content-Type': 'application/json',
        },
      }),
      invalidatesTags: ["events"],
    }),
    deleteCalendarEvent: builder.mutation({
      query: (id) => ({
        url: `/calendar-events/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["events"],
    }),
  }),
});

export const {
  useGetCategoriesQuery,
  useGetCalendarEventsQuery,
  useGetEventTypesQuery,
  useGetEventTypeQuery,
  useCreateEventTypeMutation,
  useUpdateEventTypeMutation,
  useCreateCalendarEventMutation,
  useEditCalendarEventMutation,
  useDeleteCalendarEventMutation,
} = calendarApi;
