import React, { useState } from "react";
import { Icon } from "@iconify/react";
import Dropdown from "@/components/ui/react-hook-form/Dropdown";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

const SimpleTable = ({
  columns,
  data,
  showRowsPerPageSelector = false,
  showSearch = false,
  showDropdown = false,
  showCheckboxColumn = false,
  showPagination = true, // New prop to control pagination visibility
  ...props
}) => {
  const [columnFilters, setColumnFilters] = useState([]);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedRowIds, setSelectedRowIds] = useState({});
  const [selectedFilter, setSelectedFilter] = useState("all");

  const table = useReactTable({
    data,
    columns: [
      ...(showCheckboxColumn
        ? [
            {
              id: "checkbox",
              header: ({ table }) => (
                <input
                  type="checkbox"
                  onChange={(e) => {
                    const isChecked = e.target.checked;
                    table.getRowModel().rows.forEach((row) => {
                      setSelectedRowIds((prev) => ({
                        ...prev,
                        [row.id]: isChecked,
                      }));
                    });
                  }}
                  checked={
                    Object.keys(selectedRowIds).length === table.getRowModel().rows.length
                  }
                />
              ),
              cell: ({ row }) => (
                <input
                  type="checkbox"
                  checked={selectedRowIds[row.id] || false}
                  onChange={() =>
                    setSelectedRowIds((prev) => ({
                      ...prev,
                      [row.id]: !prev[row.id],
                    }))
                  }
                />
              ),
            },
          ]
        : []),
      ...columns,
    ],
    state: {
      columnFilters,
    },
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    ...(showPagination ? { getPaginationRowModel: getPaginationRowModel() } : {}), // Pagination only if enabled
    getSortedRowModel: getSortedRowModel(),
  });

  const handleRowsPerPageChange = (event) => {
    setRowsPerPage(Number(event.target.value));
    table.setPageSize(Number(event.target.value));
  };

  const handlePageChange = (page) => {
    table.setPageIndex(page);
  };

  const handleSearchChange = (event) => {
    const value = event.target.value;
    setSearchQuery(value);
    setColumnFilters([{ id: "search", value }]);
  };

  const handleDropdownChange = (value) => {
    setSelectedFilter(value);
    setColumnFilters([{ id: "status", value }]);
  };

  return (
    <>
      {/* Conditionally render search bar or dropdown */}
      <div className="mb-4 flex justify-start items-center space-x-2">
        {showDropdown ? (
          <Dropdown
            label="Select option"
            wrapperClass="w-40 bg-gray-200 dark:bg-slate-700 p-2 rounded-md"
            labelClass="text-slate-500 dark:text-slate-400 text-sm leading-6 text-left flex items-center justify-between"
            items={[
              { label: "All", value: "all" },
              { label: "Active", value: "active" },
              { label: "Inactive", value: "inactive" },
            ]}
            onChange={handleDropdownChange}
          />
        ) : (
          showSearch && (
            <div className="relative">
              <Icon
                icon="mdi:magnify"
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                width="20"
                height="20"
              />
              <input
                type="text"
                value={searchQuery}
                onChange={handleSearchChange}
                placeholder="Search by Tuition ID"
                className="px-10 py-2 border border-gray-300 rounded-md text-sm w-64"
              />
            </div>
          )
        )}
      </div>

      {data?.length > 0 ? (
        <div className="relative w-full overflow-x-auto rounded-lg sm:rounded-lg border bg-white">
          <table className="w-full text-sm text-left rtl:text-right text-dark dark:text-dark">
            <thead className="text-xs text-dark capitalize bg-primary-200 dark:bg-gray-700 dark:text-dark">
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <th
                      key={`h_${header.id}`}
                      scope="col"
                      className="px-3 font-normal py-3 text-[14px] cursor-pointer text-left"
                      onClick={
                        header.column.getCanSort()
                          ? header.column.getToggleSortingHandler()
                          : undefined
                      }
                    >
                      <div className="flex items-center">
                        {header.column.columnDef.header}
                        {header.column.columnDef.enableSorting && (
                          <span className="ml-1 flex flex-col items-center text-black text-[8px]">
                            <span
                              className={`leading-none ${
                                header.column.getIsSorted() === "asc" ? "text-blue-50" : "opacity-50"
                              }`}
                            >
                              ▲
                            </span>
                            <span
                              className={`leading-none ${
                                header.column.getIsSorted() === "desc" ? "text-black-900" : "opacity-50"
                              }`}
                            >
                              ▼
                            </span>
                          </span>
                        )}
                      </div>
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody>
              {table.getRowModel().rows.map((row) => (
                <tr key={`r_${row.id}`} className="border-gray-100 dark:border-slate-600">
                  {row.getVisibleCells().map((cell) => (
                    <td key={`c_${cell.id}`} className="px-3 py-3 text-base text-left">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : null}

      {/* Conditional Pagination Controls */}
      {showPagination && (
        <div className="flex items-center justify-between mt-4">
          <div className="text-sm">
            {table.getState().pagination.pageIndex * rowsPerPage + 1}-
            {Math.min((table.getState().pagination.pageIndex + 1) * rowsPerPage, data.length)} of{" "}
            {data.length} entries
          </div>
          <div className="flex-grow flex justify-center items-center space-x-2">
            <button onClick={() => handlePageChange(0)} disabled={!table.getCanPreviousPage()} className="px-4 py-2 text-sm bg-black-600 text-white rounded-full">
              {"<<"}
            </button>
            <button onClick={() => handlePageChange(table.getState().pagination.pageIndex - 1)} disabled={!table.getCanPreviousPage()} className="px-4 py-2 text-sm bg-black-600 text-white rounded-full">
              {"<"}
            </button>
            {Array.from({ length: table.getPageCount() }, (_, i) => i + 1).map((pageNum) => (
              <button key={pageNum} onClick={() => handlePageChange(pageNum - 1)} className={`px-3 py-1 text-sm rounded-full border ${table.getState().pagination.pageIndex === pageNum - 1 ? "bg-black-600 text-white" : "bg-white text-black-600"}`}>
                {pageNum}
              </button>
            ))}
            <button onClick={() => handlePageChange(table.getState().pagination.pageIndex + 1)} disabled={!table.getCanNextPage()} className="px-4 py-2 text-sm bg-black-600 text-white rounded-full">
              {">"}
            </button>
            <button onClick={() => handlePageChange(table.getPageCount() - 1)} disabled={!table.getCanNextPage()} className="px-4 py-2 text-sm bg-black-600 text-white rounded-full">
              {">>"}
            </button>
          </div>
          {showRowsPerPageSelector && (
           <div className="flex items-center space-x-2">
           <span className="text-sm text-gray-600">Rows per page:</span>
           <select className="px-2 py-1 text-sm border rounded-md" value={rowsPerPage} onChange={handleRowsPerPageChange}>
           {[5, 10, 15, 20].map((val) => (
           <option key={val} value={val}>
           {val}
        </option>
      ))}
    </select>
  </div>
)}

        </div>
      )}
    </>
  );
};

export default SimpleTable;
