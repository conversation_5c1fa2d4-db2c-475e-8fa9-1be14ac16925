import React, { useMemo } from "react";
import { useFetchDataQuery } from "@/store/api/apiSlice";
import InputSelect from "@/components/ui/formik-form/InputSelect";

const SchoolList = ({ value, onChange, label = "School" }) => {
  // Fetch all schools
  const { data: schoolsData, isLoading, error } = useFetchDataQuery("/schools");

  // Memoize options for performance
  const schoolOptions = useMemo(() => {
    // Handle different possible data structures
    let schoolsArray = [];
    
    if (schoolsData?.data?.data) {
      // Paginated structure: { data: { data: [...] } }
      schoolsArray = schoolsData.data.data;
    } else if (schoolsData?.data && Array.isArray(schoolsData.data)) {
      // Direct array: { data: [...] }
      schoolsArray = schoolsData.data;
    } else if (Array.isArray(schoolsData)) {
      // Direct array: [...]
      schoolsArray = schoolsData;
    }
    
    if (schoolsArray && Array.isArray(schoolsArray) && schoolsArray.length > 0) {
      const options = schoolsArray
        .filter(school => school && school.id && school.name) // Filter out invalid entries
        .map((school) => ({
          value: String(school.id),
          label: school.name,
        }));
      // Add "All Schools" option at the beginning
      return [
        { value: "", label: "All Schools" },
        ...options
      ];
    }
    return [{ value: "", label: "All Schools" }];
  }, [schoolsData]);

  return (
    <InputSelect
      label={label}
      options={schoolOptions}
      value={value?.value || value}
      onChange={onChange}
      isLoading={isLoading}
      placeholder={
        isLoading 
          ? "Loading schools..." 
          : error 
            ? "Error loading schools" 
            : "Select a school"
      }
    />
  );
};

export default SchoolList;
