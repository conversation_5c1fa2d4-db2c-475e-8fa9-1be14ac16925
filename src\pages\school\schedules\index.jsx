import React, { useState, useRef, useEffect } from "react";
import { Icon } from "@iconify/react";
import { useNavigate } from "react-router-dom";
import Card from '@/components/ui/Card';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';
import Button from "@/components/ui/Button";
import { v4 as uuidv4 } from 'uuid';
import Modal from "@/components/ui/Modal";
import SchoolList from '@/components/SchoolList';
import { 
  useGetEventTypesQuery,
  useCreateEventTypeMutation,
  useUpdateEventTypeMutation,
  useCreateCalendarEventMutation,
  useEditCalendarEventMutation
} from '@/store/api/app/calendarSlice';

const Schedules = () => {
  const navigate = useNavigate();
  const calendarRef = useRef(null);
  const [viewType, setViewType] = useState('dayGridMonth');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [showEventModal, setShowEventModal] = useState(false);
  const [eventForm, setEventForm] = useState({
    id: '',
    title: '',
    start: '',
    end: '',
    allDay: false,
    category: 'class',
    student: '',
    teacher: ''
  });
  const [isEditMode, setIsEditMode] = useState(false);
  
  // API hooks
  const { data: eventTypes, isLoading: eventTypesLoading } = useGetEventTypesQuery();
  const [createEventType, { isLoading: isCreatingEventType }] = useCreateEventTypeMutation();
  const [updateEventType, { isLoading: isUpdatingEventType }] = useUpdateEventTypeMutation();
  const [createCalendarEvent, { isLoading: isCreatingEvent }] = useCreateCalendarEventMutation();
  const [editCalendarEvent, { isLoading: isEditingEvent }] = useEditCalendarEventMutation();
  
  // Add state for new event type
  const [newEventType, setNewEventType] = useState('');
  const [showNewEventTypeForm, setShowNewEventTypeForm] = useState(false);
  
  // Function to handle event type creation
  const handleCreateEventType = async (e) => {
    e.preventDefault();
    if (!newEventType.trim()) return;
    
    try {
      await createEventType({ type_name: newEventType.trim() }).unwrap();
      setNewEventType('');
      setShowNewEventTypeForm(false);
    } catch (error) {
      console.error('Failed to create event type:', error);
    }
  };
  
  // Sample events data - replace with your actual data source
  const [events, setEvents] = useState([
    {
      id: '1',
      title: 'Math Class',
      start: new Date(new Date().setHours(10, 0, 0, 0)),
      end: new Date(new Date().setHours(11, 30, 0, 0)),
      classNames: ['event-class'],
      extendedProps: { category: 'class' }
    },
    {
      id: '2',
      title: 'Science Exam',
      start: new Date(new Date().setDate(new Date().getDate() + 2)),
      allDay: true,
      classNames: ['event-exam'],
      extendedProps: { category: 'exam' }
    },
    {
      id: '3',
      title: 'Parent-Teacher Meeting',
      start: new Date(new Date().setDate(new Date().getDate() + 5)),
      end: new Date(new Date().setDate(new Date().getDate() + 5)),
      classNames: ['event-meeting'],
      extendedProps: { category: 'meeting' }
    }
  ]);

  // New states for students and teachers
  const [students, setStudents] = useState([]);
  const [teachers, setTeachers] = useState([]);
  const [loading, setLoading] = useState(false);

  // Fetch students and teachers data on component mount

  // Update calendar when view changes
  useEffect(() => {
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      calendarApi.changeView(viewType);
      calendarApi.gotoDate(currentDate);
    }
  }, [viewType, currentDate]);

  // First, add this near your other useEffect hooks to log the structure
  useEffect(() => {
    if (eventTypes) {
      console.log('Event Types Response Structure:', eventTypes);
    }
  }, [eventTypes]);

  const handleDateClick = (info) => {
    // Open event creation modal with the clicked date
    const clickedDate = new Date(info.date);
    
    // Set default times (9 AM to 10 AM)
    const startDate = new Date(clickedDate);
    startDate.setHours(9, 0, 0);
    
    const endDate = new Date(clickedDate);
    endDate.setHours(10, 0, 0);
    
    setEventForm({
      id: '',
      title: '',
      start: formatDateTimeForInput(startDate),
      end: formatDateTimeForInput(endDate),
      allDay: info.allDay,
      category: 'class',
      student: '',
      teacher: ''
    });
    
    setIsEditMode(false);
    setShowEventModal(true);
  };

  const handleEventClick = (info) => {
    const event = info.event;
    
    // Open event edit modal with the event data
    setEventForm({
      id: event.id,
      title: event.title,
      start: formatDateTimeForInput(event.start),
      end: event.end ? formatDateTimeForInput(event.end) : '',
      allDay: event.allDay,
      category: event.extendedProps.category || 'class',
      student: event.extendedProps.student || '',
      teacher: event.extendedProps.teacher || ''
    });
    
    setIsEditMode(true);
    setShowEventModal(true);
  };

  // Format date for datetime-local input
  const formatDateTimeForInput = (date) => {
    if (!date) return '';
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  // Handle creating a new event
  const handleCreateNewEvent = () => {
    // Reset form and open modal
    const now = new Date();
    const oneHourLater = new Date(now);
    oneHourLater.setHours(now.getHours() + 1);
    
    setEventForm({
      id: '',
      title: '',
      start: formatDateTimeForInput(now),
      end: formatDateTimeForInput(oneHourLater),
      allDay: false,
      category: 'class',
      student: '',
      teacher: ''
    });
    
    setIsEditMode(false);
    setShowEventModal(true);
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setEventForm({
      ...eventForm,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle form submission
  const handleSubmitEvent = async (e) => {
    e.preventDefault();
    
    // Find the selected event type
    const selectedEventType = (() => {
      let eventTypesList = [];
      
      if (Array.isArray(eventTypes)) {
        eventTypesList = eventTypes;
      } else if (eventTypes?.data && Array.isArray(eventTypes.data)) {
        eventTypesList = eventTypes.data;
      } else if (eventTypes?.data?.data && Array.isArray(eventTypes.data.data)) {
        eventTypesList = eventTypes.data.data;
      }
      
      return eventTypesList.find(type => 
        (type.type_name || type.name || type.type) === eventForm.category
      );
    })();
    
    const eventData = {
      title: eventForm.title,
      start: new Date(eventForm.start).toISOString(),
      end: eventForm.end ? new Date(eventForm.end).toISOString() : null,
      allDay: eventForm.allDay,
      category: eventForm.category,
      student: eventForm.student,
      teacher: eventForm.teacher,
      event_type_id: selectedEventType?.id
    };
    
    try {
      if (isEditMode) {
        // Update existing event
        await editCalendarEvent({ 
          id: eventForm.id, 
          event: eventData 
        }).unwrap();
      } else {
        // Create new event
        await createCalendarEvent(eventData).unwrap();
      }
      
      // Close modal
      setShowEventModal(false);
      
      // Refresh calendar events (this will happen automatically if you're using RTK Query)
    } catch (error) {
      console.error('Failed to save event:', error);
    }
  };

  // Handle event deletion
  const handleDeleteEvent = () => {
    if (isEditMode && eventForm.id) {
      setEvents(events.filter(event => event.id !== eventForm.id));
      setShowEventModal(false);
    }
  };

  // Navigation handlers that properly update the calendar
  const handlePrevious = () => {
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      calendarApi.prev();
      setCurrentDate(calendarApi.getDate());
    }
  };

  const handleNext = () => {
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      calendarApi.next();
      setCurrentDate(calendarApi.getDate());
    }
  };

  const handleToday = () => {
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      calendarApi.today();
      setCurrentDate(calendarApi.getDate());
    }
  };

  const handleViewChange = (newView) => {
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      calendarApi.changeView(newView);
      setViewType(newView);
    }
  };

  // Year navigation
  const handlePreviousYear = () => {
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      const currentYear = currentDate.getFullYear();
      const newDate = new Date(currentDate);
      newDate.setFullYear(currentYear - 1);
      calendarApi.gotoDate(newDate);
      setCurrentDate(newDate);
    }
  };

  const handleNextYear = () => {
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      const currentYear = currentDate.getFullYear();
      const newDate = new Date(currentDate);
      newDate.setFullYear(currentYear + 1);
      calendarApi.gotoDate(newDate);
      setCurrentDate(newDate);
    }
  };

  // Format the current date based on the view type
  const getFormattedDate = () => {
    if (!currentDate) return '';
    
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June', 
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    const shortMonths = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    
    const days = [
      'Sunday', 'Monday', 'Tuesday', 'Wednesday', 
      'Thursday', 'Friday', 'Saturday'
    ];
    
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const date = currentDate.getDate();
    const day = currentDate.getDay();
    
    switch (viewType) {
      case 'dayGridMonth':
        return `${months[month]} ${year}`;
      case 'timeGridWeek':
        return `Week of ${shortMonths[month]} ${date}, ${year}`;
      case 'timeGridDay':
        return `${days[day]}, ${months[month]} ${date}, ${year}`;
      case 'listWeek':
        const endDate = new Date(currentDate);
        endDate.setDate(endDate.getDate() + 6);
        const endMonth = endDate.getMonth();
        const endDateNum = endDate.getDate();
        const endYear = endDate.getFullYear();
        return `Events: ${shortMonths[month]} ${date} - ${shortMonths[endMonth]} ${endDateNum}, ${endYear}`;
      default:
        return `${months[month]} ${year}`;
    }
  };

  // Sample event categories for the legend
  const eventCategories = [
    { name: 'Classes', color: '#4669FA', icon: 'solar:book-bold-duotone', value: 'class' },
    { name: 'Exams', color: '#F1841B', icon: 'solar:document-bold-duotone', value: 'exam' },
    { name: 'Meetings', color: '#50C793', icon: 'solar:users-group-rounded-bold-duotone', value: 'meeting' },
    { name: 'Holidays', color: '#FA916B', icon: 'solar:palm-bold-duotone', value: 'holiday' }
  ];

  const [selectedSchool, setSelectedSchool] = useState(null);

  return (
    <div className="p-6">
      <Card 
        title={<h3 className="text-xl font-medium text-slate-900 dark:text-white flex items-center gap-2">
          <Icon icon="solar:calendar-bold-duotone" className="text-primary-500 text-2xl" />
          Academic Calendar
        </h3>}
        subtitle="View and manage your school schedule"
        headerslot={
          <div className="flex items-center space-x-2">
            
            <div className="min-w-[200px] z-50 ">
              <SchoolList
                value={selectedSchool}
                onChange={setSelectedSchool}
                label=""
              />
            </div>
            {selectedSchool && (
              <Button
                className="bg-primary-500 hover:bg-primary-600 text-white border-0 shadow-md hover:shadow-lg transition-all duration-200"
                icon="solar:add-circle-bold"
                onClick={handleCreateNewEvent}
              >
                New Event
              </Button>
            )}
          </div>
        }
        className="shadow-xl dark:bg-slate-800 border-0 rounded-xl overflow-hidden"
        bodyClass="p-0"
      >
        <div className="ultra-calendar">
          {/* Custom Calendar Header */}
          <div className="calendar-custom-header px-6 py-4 flex flex-col md:flex-row justify-between items-center border-b border-slate-200 dark:border-slate-700">
            {selectedSchool && (
              <div className="calendar-navigation flex items-center space-x-3 mb-4 md:mb-0">
                {/* Year navigation buttons */}
                {viewType === 'dayGridMonth' && (
                  <button 
                    onClick={handlePreviousYear}
                    className="nav-btn year-btn bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 h-10 w-10 rounded-full flex items-center justify-center shadow-sm hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:text-primary-500 transition-all duration-200"
                  >
                    <Icon icon="solar:double-alt-arrow-left-linear" className="text-xl" />
                  </button>
                )}
                
                <button 
                  onClick={handlePrevious}
                  className="nav-btn prev-btn bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 h-10 w-10 rounded-full flex items-center justify-center shadow-sm hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:text-primary-500 transition-all duration-200"
                >
                  <Icon icon="solar:arrow-left-linear" className="text-xl" />
                </button>
                
                <div className="current-date-display">
                  <h3 className="text-xl font-bold text-slate-800 dark:text-white">
                    {getFormattedDate()}
                  </h3>
                </div>
                
                <button 
                  onClick={handleNext}
                  className="nav-btn next-btn bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 h-10 w-10 rounded-full flex items-center justify-center shadow-sm hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:text-primary-500 transition-all duration-200"
                >
                  <Icon icon="solar:arrow-right-linear" className="text-xl" />
                </button>
                
                {/* Year navigation buttons */}
                {viewType === 'dayGridMonth' && (
                  <button 
                    onClick={handleNextYear}
                    className="nav-btn year-btn bg-white dark:bg-slate-700 text-slate-600 dark:text-slate-300 h-10 w-10 rounded-full flex items-center justify-center shadow-sm hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:text-primary-500 transition-all duration-200"
                  >
                    <Icon icon="solar:double-alt-arrow-right-linear" className="text-xl" />
                  </button>
                )}
                
                <button 
                  onClick={handleToday}
                  className="today-btn bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-300 border border-slate-200 dark:border-slate-600 rounded-full px-4 py-2 text-sm font-medium shadow-sm hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:text-primary-500 transition-all duration-200 ml-2"
                >
                  Today
                </button>
                
              </div>
            )}
            
            <div className="flex items-center gap-3 mt-4 md:mt-0">
              {selectedSchool && (
                <div className="calendar-view-selector inline-flex rounded-lg shadow-sm bg-slate-100 dark:bg-slate-700 p-1">
                  <button 
                    onClick={() => handleViewChange('dayGridMonth')}
                    className={`px-4 py-2 text-sm font-medium rounded-md transition-all ${
                      viewType === 'dayGridMonth' 
                        ? 'bg-white dark:bg-slate-800 text-primary-500 shadow-sm' 
                        : 'text-slate-600 dark:text-slate-300 hover:text-primary-500'
                    }`}
                  >
                    <Icon icon="solar:calendar-linear" className="mr-1 inline-block" />
                    Month
                  </button>
                  <button 
                    onClick={() => handleViewChange('listWeek')}
                    className={`px-4 py-2 text-sm font-medium rounded-md transition-all ${
                      viewType === 'listWeek' 
                        ? 'bg-white dark:bg-slate-800 text-primary-500 shadow-sm' 
                        : 'text-slate-600 dark:text-slate-300 hover:text-primary-500'
                    }`}
                  >
                    <Icon icon="solar:list-linear" className="mr-1 inline-block" />
                    List
                  </button>
                </div>
              )}
            </div>
          </div>
          {/* Only show calendar if a school is selected */}
          {selectedSchool ? (
            <>
              {/* Event Categories Legend */}
              <div className="event-categories-legend px-6 py-3 flex flex-wrap gap-4 bg-slate-50 dark:bg-slate-800/50 border-b border-slate-200 dark:border-slate-700">
                {eventCategories.map((category, index) => (
                  <div key={index} className="event-category-item flex items-center">
                    <div className="category-color-dot h-3 w-3 rounded-full mr-2" style={{ backgroundColor: category.color }}></div>
                    <Icon icon={category.icon} className="mr-1 text-lg" style={{ color: category.color }} />
                    <span className="text-xs font-medium text-slate-600 dark:text-slate-400">{category.name}</span>
                  </div>
                ))}
              </div>
              {/* Calendar Component */}
              <div className="calendar-container p-4">
                <FullCalendar
                  ref={calendarRef}
                  plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin]}
                  initialView={viewType}
                  headerToolbar={false} // We're using our custom header
                  events={events}
                  eventClassNames={(arg) => {
                    return arg.event.extendedProps.category === 'class' ? 'event-class' :
                           arg.event.extendedProps.category === 'exam' ? 'event-exam' :
                           arg.event.extendedProps.category === 'meeting' ? 'event-meeting' : 
                           'event-holiday';
                  }}
                  editable={true}
                  selectable={true}
                  selectMirror={true}
                  dayMaxEvents={3}
                  weekends={true}
                  dateClick={handleDateClick}
                  eventClick={handleEventClick}
                  height="650px"
                  nowIndicator={true}
                  navLinks={true}
                  businessHours={{
                    daysOfWeek: [1, 2, 3, 4, 5],
                    startTime: '08:00',
                    endTime: '18:00',
                  }}
                  eventTimeFormat={{
                    hour: '2-digit',
                    minute: '2-digit',
                    meridiem: 'short'
                  }}
                  slotLabelFormat={{
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                  }}
                  dayHeaderFormat={{
                    weekday: 'short',
                    day: 'numeric'
                  }}
                  views={{
                    dayGridMonth: {
                      titleFormat: { month: 'long', year: 'numeric' },
                      dayHeaderFormat: { weekday: 'short' }
                    },
                    timeGridWeek: {
                      titleFormat: { month: 'short', day: 'numeric', year: 'numeric' },
                      dayHeaderFormat: { weekday: 'short', day: 'numeric' }
                    },
                    timeGridDay: {
                      titleFormat: { month: 'long', day: 'numeric', year: 'numeric' },
                      dayHeaderFormat: { weekday: 'long', day: 'numeric', month: 'long' }
                    },
                    listWeek: {
                      titleFormat: { month: 'long', year: 'numeric' },
                      dayHeaderFormat: { weekday: 'long', day: 'numeric', month: 'long' }
                    }
                  }}
                  datesSet={(dateInfo) => {
                    // Keep the state in sync with the calendar's current date
                    setCurrentDate(dateInfo.view.currentStart);
                  }}
                />
              </div>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center min-h-[60vh] py-16">
              <div className="bg-orange-50 shadow-lg rounded-2xl flex flex-col items-center justify-center max-w-2xl w-full py-20 px-10 border border-orange-100">
                <span className="mb-4">
                  <Icon icon="heroicons-outline:globe-alt" className="text-orange-400" width={48} height={48} />
                </span>
                <h2 className="text-2xl font-bold text-orange-700 mb-2 text-center">
                  Please select a school
                </h2>
                <p className="text-gray-600 text-base text-center max-w-md">
                  Use the dropdown above to view the schedule for a specific school.
                </p>
              </div>
            </div>
          )}
        </div>
      </Card>
      
      {/* Event Creation/Edit Modal */}
      {showEventModal && (
        <Modal
          title={isEditMode ? "Edit Event" : "Create New Event"}
          labelclassName="btn-outline-dark"
          activeModal={showEventModal}
          onClose={() => setShowEventModal(false)}
          centered
          scrollContent
          themeClass="bg-slate-50 dark:bg-slate-800 rounded-lg shadow-xl"
          titleClass="text-xl font-semibold text-slate-900 dark:text-white"
        >
          <form onSubmit={handleSubmitEvent} className="space-y-4">
            <div className="mb-3">
               <div className="grid grid-cols-1 md:grid-cols-2 gap-4">  
            </div>
              <label className="form-label">Event Title</label>
              <input
                type="text"
                name="title"
                value={eventForm.title}
                onChange={handleInputChange}
                className="form-control py-2 px-3 border border-slate-200 dark:border-slate-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent w-full dark:bg-slate-900"
                placeholder="Enter event title"
                required
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="mb-3">
                <label className="form-label">Start Date & Time</label>
                <input
                  type="datetime-local"
                  name="start"
                  value={eventForm.start}
                  onChange={handleInputChange}
                  className="form-control py-2 px-3 border border-slate-200 dark:border-slate-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent w-full dark:bg-slate-900"
                  required
                />
              </div>
              
              <div className="mb-3">
                <label className="form-label">End Date & Time</label>
                <input
                  type="datetime-local"
                  name="end"
                  value={eventForm.end}
                  onChange={handleInputChange}
                  className="form-control py-2 px-3 border border-slate-200 dark:border-slate-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent w-full dark:bg-slate-900"
                />
              </div>
            </div>
            
            <div className="mb-3">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="allDay"
                  id="allDay"
                  checked={eventForm.allDay}
                  onChange={handleInputChange}
                  className="form-checkbox h-5 w-5 text-primary-500 rounded border-slate-300 dark:border-slate-700 focus:ring-primary-500 dark:bg-slate-900"
                />
                <label htmlFor="allDay" className="ml-2 form-label">All Day Event</label>
              </div>
            </div>
            
            <div className="mb-3">
              <label className="form-label">Event Type</label>
              <select
                name="category"
                value={eventForm.category}
                onChange={handleInputChange}
                className="form-select py-2 px-3 border border-slate-200 dark:border-slate-700 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent w-full dark:bg-slate-900"
              >
                {eventTypesLoading ? (
                  <option>Loading...</option>
                ) : (
                  (() => {
                    let eventTypesList = [];
                    
                    if (Array.isArray(eventTypes)) {
                      eventTypesList = eventTypes;
                    } else if (eventTypes?.data && Array.isArray(eventTypes.data)) {
                      eventTypesList = eventTypes.data;
                    } else if (eventTypes?.data?.data && Array.isArray(eventTypes.data.data)) {
                      eventTypesList = eventTypes.data.data;
                    }
                    
                    return eventTypesList.map((type) => (
                      <option key={type.id} value={type.type_name}>
                        {type.type_name}
                      </option>
                    ));
                  })()
                )}
              </select>
            </div>

            
            <div className="flex justify-between mt-6">
              {isEditMode && (
                <Button
                  type="button"
                  onClick={handleDeleteEvent}
                  className="bg-red-500 hover:bg-red-600 text-white"
                >
                  Delete
                </Button>
              )}
              
              <div className="flex gap-2 ml-auto">
                <Button
                  type="button"
                  onClick={() => setShowEventModal(false)}
                  className="bg-slate-200 hover:bg-slate-300 text-slate-700 dark:bg-slate-700 dark:hover:bg-slate-600 dark:text-white"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-primary-500 hover:bg-primary-600 text-white"
                >
                  {isEditMode ? "Update" : "Create"}
                </Button>
              </div>
            </div>
          </form>
        </Modal>
      )}
    </div>
  );
};

export default Schedules;

