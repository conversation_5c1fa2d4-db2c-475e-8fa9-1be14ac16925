import React, { useEffect, useState } from "react";
import { useField } from "formik";
// import { <PERSON>a<PERSON><PERSON>, FaEyeSlash } from "react-icons/fa";
import { Icon } from "@iconify/react";
const CalculatePasswordStrength = (password) => {
  if (!password) return 0;

  let strength = 0;
  if (password.length >= 8) strength += 20;
  if (/[A-Z]/.test(password)) strength += 20;
  if (/[a-z]/.test(password)) strength += 20;
  if (/[0-9]/.test(password)) strength += 20;
  if (/[!@#$%^&*]/.test(password)) strength += 20;

  return strength;
};

const getStrengthColor = (strength) => {
  if (strength <= 20) return "bg-red-500";
  if (strength <= 40) return "bg-orange-500";
  if (strength <= 60) return "bg-yellow-500";
  if (strength <= 80) return "bg-blue-500";
  return "bg-green-500";
};

const getStrengthText = (strength) => {
  if (strength <= 20) return "Very Weak";
  if (strength <= 40) return "Weak";
  if (strength <= 60) return "Medium";
  if (strength <= 80) return "Strong";
  return "Very Strong";
};

const InputFieldPasswordWithIcon = ({
  className,
  label,
  required,
  placeholder,
  icon,
  strongCheck = true,
  ...props
}) => {
  const [field, meta] = useField(props);
  const [showPassword, setShowPassword] = useState(false);
  const isError = meta.touched && meta.error;
  const strength = CalculatePasswordStrength(field.value);

  // Browser-specific style to hide default eye icon
  const hideBrowserRevealIcon = `
    input::-ms-reveal, input::-ms-clear {
      display: none !important;
    }
    input::-webkit-credentials-auto-fill-button,
    input::-webkit-inner-spin-button,
    input::-webkit-contacts-auto-fill-button {
      visibility: hidden;
      display: none !important;
      pointer-events: none;
      height: 0;
      width: 0;
    }
    input[type="password"] {
      -moz-appearance: textfield !important;
    }
  `;

  return (
    <div>
      <style>{hideBrowserRevealIcon}</style>
      {label && (
        <label
          htmlFor={props.id || props.name}
          className="block text-secondary-800 text-base mb-2 font-semibold"
        >
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <div className="relative">
        <span className="absolute inset-y-0 left-0 pl-3 flex items-center">
          <Icon icon={icon} width="20" height="20" />
        </span>
        <input
          {...field}
          {...props}
          type={showPassword ? "text" : "password"}
          placeholder={placeholder}
          className={`rounded-lg pl-10 pr-10 appearance-none border w-full p-3 text-gray-700 leading-tight focus:outline-none placeholder:text-sm ${className} ${
            isError ? "border-red-500" : ""
          }`}
          autoComplete="new-password"
        />
        <button
          type="button"
          onClick={() => setShowPassword(!showPassword)}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
        >
          {/* {showPassword ? <FaEyeSlash size={18} /> : <FaEye size={18} />} */}
        </button>
      </div>
      {strongCheck && field.value && (
        <div className="mt-2">
          <div className="flex justify-between mb-1">
            <div className="h-2 flex-1 rounded-full bg-slate-100 overflow-hidden">
              <div
                className={`h-full transition-all ${getStrengthColor(
                  strength
                )}`}
                style={{ width: `${strength}%` }}
              />
            </div>
          </div>
          <p
            className={`text-xs ${getStrengthColor(strength).replace(
              "bg-",
              "text-"
            )}`}
          >
            {getStrengthText(strength)}
          </p>
        </div>
      )}
      {isError && <span className="text-red-500 text-xs">{meta.error}</span>}
    </div>
  );
};

export default InputFieldPasswordWithIcon;
