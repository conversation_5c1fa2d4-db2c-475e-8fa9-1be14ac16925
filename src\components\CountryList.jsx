import React, { useEffect, useState } from "react";
import { useFetchDataQuery } from "@/store/api/apiSlice";
import InputSelect from "@/components/ui/formik-form/InputSelect";
import Select from "react-select";

const CountryList = ({ 
  selectedCountry, 
  onCountryChange, 
  label = "Filter by Country",
  placeholder = "Select country",
  required = false,
  className = ""
}) => {
  const { data: countriesData, isLoading } = useFetchDataQuery("countries");
  const [countryOptions, setCountryOptions] = useState([]);

  useEffect(() => {
    if (countriesData?.data) {
      const options = countriesData.data.map(country => ({
        value: country.id,
        label: country.name
      }));
      // Add "All Countries" option at the beginning
      const allOptions = [
        { value: "", label: "All Countries" },
        ...options
      ];
      setCountryOptions(allOptions);
    }
  }, [countriesData]);

  const handleCountryChange = (selectedOption) => {
    if (onCountryChange) {
      onCountryChange(selectedOption?.value || "");
    }
  };

  // Custom component to remove label when not needed
  const CustomInputSelect = ({ label, ...props }) => {
    if (!label) {
      return (
        <div className="w-full">
          <Select
            value={props.value !== undefined 
              ? countryOptions.find((opt) => opt.value === props.value) || null
              : null
            }
            options={countryOptions}
            onChange={handleCountryChange}
            placeholder={props.placeholder}
            classNamePrefix="react-select"
            className="react-select-container rounded-md border border-gray-300"
            styles={{
              control: (base, state) => ({
                ...base,
                boxShadow: 'none',
                border: 'none',
                '&:hover': {
                  border: 'none'
                }
              }),
              input: (base) => ({
                ...base,
                minHeight: "40px",
              }),
              valueContainer: (base) => ({
                ...base,
                minHeight: "40px",
                display: "flex",
                alignItems: "center",
              }),
              container: (base) => ({
                ...base,
                minHeight: "50px",
              }),
            }}
          />
        </div>
      );
    }
    
    return (
      <InputSelect
        label={label}
        options={countryOptions}
        value={props.value}
        onChange={handleCountryChange}
        placeholder={props.placeholder}
        required={props.required}
        valueKey="value"
        labelKey="label"
      />
    );
  };

  if (isLoading) {
    return (
      <div className={`w-full ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded mb-2"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      <CustomInputSelect
        label={label}
        value={selectedCountry}
        placeholder={placeholder}
        required={required}
      />
    </div>
  );
};

export default CountryList;
