import { useFetchDataQuery } from "@/store/api/apiSlice";

const useAllArea = (upazilaId) => {
    const { data: areaData, error: areaError, isLoading: areaIsLoading } = useFetchDataQuery(
        upazilaId ? `/open/area-list-by-id/${upazilaId}` : null,
        {
            skip: !upazilaId
        }
    );

    // Transform the API response into dropdown-friendly format
    const areaOptions =
        areaData?.data?.map((item) => ({
            value: item.id,
            label: item.name,
        })) || [];

    return { areaOptions, areaError, areaIsLoading };
};

export default useAllArea;
