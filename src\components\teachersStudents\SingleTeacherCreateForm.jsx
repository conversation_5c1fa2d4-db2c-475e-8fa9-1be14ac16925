import React, { useState, useEffect } from "react";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import Button from "@/components/ui/Button";
import { usePostDataMutation, usePutDataMutation } from "@/store/api/apiSlice";
import { toast } from "react-toastify";
import { Icon } from "@iconify/react";

const SingleTeacherCreateForm = ({ teacherData, isUpdateMode = false, onSuccess }) => {
  const [postData] = usePostDataMutation();
  const [putData] = usePutDataMutation();
  const [submitting, setSubmitting] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);
  const [uploadImage, setUploadImage] = useState(null);

  useEffect(() => {
    if (teacherData?.avatar) {
      setPreviewImage(teacherData.avatar);
    }
  }, [teacherData]);

  const validationSchema = Yup.object().shape({
    name: Yup.string().required("Name is required"),
    name_jp: Yup.string().required("Japanese name is required"),
    email: Yup.string().email("Invalid email").required("Email is required"),
    phone: Yup.string().required("Phone number is required"),
    gender: Yup.string().required("Gender is required"),
    nationality: Yup.string().required("Nationality is required"),
    experience: Yup.string().required("Experience is required"),
  });

  const getInitialValues = () => {
    if (isUpdateMode && teacherData) {
      return {
        name: teacherData.name || "",
        name_jp: teacherData.name_jp || "",
        email: teacherData.email || "",
        phone: teacherData.phone || "",
        gender: teacherData.gender || "",
        nationality: teacherData.nationality || "",
        experience: teacherData.experience || "",
        rating: teacherData.rating || 0,
        profile_image: "",
      };
    }
    
    return {
      name: "",
      name_jp: "",
      email: "",
      phone: "",
      gender: "",
      nationality: "",
      experience: "",
      rating: 0,
      profile_image: "",
    };
  };

  const handleSubmit = async (values, { setSubmitting }) => {
    setSubmitting(true);
    
    const formData = new FormData();
    
    // Add all form fields to formData
    Object.keys(values).forEach(key => {
      if (key !== "profile_image") {
        formData.append(key, values[key]);
      }
    });
    
    // Add profile image if it exists
    if (uploadImage) {
      formData.append("profile_image", uploadImage);
    }
    
    try {
      // Add method parameter for update
      const endpoint = isUpdateMode ? `/users/${teacherData.id}` : '/users';
      const method = isUpdateMode ? 'PUT' : 'POST';
      
      const response = isUpdateMode 
        ? await putData({ url: endpoint, body: formData }).unwrap()
        : await postData({ url: endpoint, body: formData }).unwrap();
        
      if (response?.status === 200) {
        toast.success(isUpdateMode ? 'Teacher updated successfully' : 'Teacher created successfully');
        if (onSuccess) onSuccess();
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error(error?.data?.message || 'An error occurred while saving teacher data');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Formik
      initialValues={getInitialValues()}
      validationSchema={validationSchema}
      onSubmit={handleSubmit}
    >
      {({ isSubmitting, values, setFieldValue }) => (
        <Form>
          <div className="mb-3">
            {/* Custom Image Upload Component */}
            <div className="border border-dashed border-gray-300 rounded-lg p-6 relative">
              {previewImage ? (
                <div className="flex justify-center">
                  <div className="relative inline-block">
                    <img 
                      src={typeof previewImage === 'string' ? previewImage : URL.createObjectURL(previewImage)} 
                      alt="Profile" 
                      className="w-20 h-20 rounded-md object-cover"
                    />
                    <button
                      type="button"
                      onClick={() => {
                        setPreviewImage(null);
                        setUploadImage(null);
                        setFieldValue("profile_image", "");
                      }}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1"
                    >
                      <Icon icon="heroicons-outline:x" className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-center">
                  <div className="mx-auto h-12 w-12 text-gray-400">
                    <Icon icon="heroicons-outline:photograph" className="h-12 w-12" />
                  </div>
                  <div className="mt-2">
                    <label htmlFor="profile_image" className="cursor-pointer">
                      <span className="mt-2 block text-sm font-medium text-gray-700">
                        Upload a profile picture
                      </span>
                      <input
                        id="profile_image"
                        name="profile_image"
                        type="file"
                        className="sr-only"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files[0];
                          if (file) {
                            setPreviewImage(file);
                            setUploadImage(file);
                            setFieldValue("profile_image", file);
                          }
                        }}
                      />
                    </label>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="mb-3">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Name (English)
              </label>
              <Field
                type="text"
                name="name"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              />
              <ErrorMessage name="name" component="div" className="text-red-500 text-sm mt-1" />
            </div>

            <div className="mb-3">
              <label htmlFor="name_jp" className="block text-sm font-medium text-gray-700">
                Name (Japanese)
              </label>
              <Field
                type="text"
                name="name_jp"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              />
              <ErrorMessage name="name_jp" component="div" className="text-red-500 text-sm mt-1" />
            </div>

            <div className="mb-3">
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <Field
                type="email"
                name="email"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              />
              <ErrorMessage name="email" component="div" className="text-red-500 text-sm mt-1" />
            </div>

            <div className="mb-3">
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                Phone
              </label>
              <Field
                type="text"
                name="phone"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              />
              <ErrorMessage name="phone" component="div" className="text-red-500 text-sm mt-1" />
            </div>

            <div className="mb-3">
              <label htmlFor="gender" className="block text-sm font-medium text-gray-700">
                Gender
              </label>
              <Field
                as="select"
                name="gender"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              >
                <option value="">Select Gender</option>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
              </Field>
              <ErrorMessage name="gender" component="div" className="text-red-500 text-sm mt-1" />
            </div>

            <div className="mb-3">
              <label htmlFor="nationality" className="block text-sm font-medium text-gray-700">
                Nationality
              </label>
              <Field
                type="text"
                name="nationality"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              />
              <ErrorMessage name="nationality" component="div" className="text-red-500 text-sm mt-1" />
            </div>

            <div className="mb-3">
              <label htmlFor="experience" className="block text-sm font-medium text-gray-700">
                Experience
              </label>
              <Field
                type="text"
                name="experience"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              />
              <ErrorMessage name="experience" component="div" className="text-red-500 text-sm mt-1" />
            </div>

            <div className="mb-3">
              <label htmlFor="rating" className="block text-sm font-medium text-gray-700">
                Rating (0-5)
              </label>
              <Field
                type="number"
                name="rating"
                min="0"
                max="5"
                step="0.1"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              />
              <ErrorMessage name="rating" component="div" className="text-red-500 text-sm mt-1" />
            </div>
          </div>
      
          <div className="flex items-center justify-end w-full">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="mt-4 bg-primary-500 text-white hover:bg-primary-400"
            >
              {isSubmitting ? (isUpdateMode ? "Updating..." : "Creating...") : (isUpdateMode ? "Update Teacher" : "Create Teacher")}
            </Button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default SingleTeacherCreateForm;