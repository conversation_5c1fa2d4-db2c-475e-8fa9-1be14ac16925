import React, { useEffect, useState } from 'react'
import BulkUsersUpload from '@/components/teachersStudents/BulkUsersUpload';
import { studentBulkUpload } from '@/constant/apiUrls';
import { usePostDataMutation } from '@/store/api/apiSlice';
import { setBreadcrumbs, setPageTitle } from '@/store/common/commonSlice';
import { useDispatch } from 'react-redux';
import { fa } from '@faker-js/faker';
import { useNavigate } from 'react-router-dom';
import { Loader } from 'lucide-react';
import Loading from '@/components/Loading';
const BulkUploadStudentList = () => {
  const [postData, { isLoading: isSubmitting }] = usePostDataMutation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  useEffect(() => {
    const breadCrumb = [
        { label: "Students", path: "/all-students" },
        { label: "Students Bulk Upload", path: "#" }
      ];
      dispatch(setBreadcrumbs(breadCrumb));
      dispatch(setPageTitle({
          title : "Students Bulk Upload",
          isBackButton : true
      }));
  }, [dispatch]);
  const [studentKeys, setStudentKeys] = useState(Object.keys({
     "name": "",
      "name_jp": "",
      "username": "",
      "profile_image": "",
      "phone": "",
      "email": "",
      "age": 0,
      "gender": "",
      "experience": "",
      "school_id": 0,
      "grade_id": 0,
      "country_id": 0,
      "hobby": "",
      "favorite_movie": "",
      "specialty": "",
      "user_type": "",
      "rating": 0,
      "favorite_added": 0,
      "is_active": 0,
      "password": ""
  }));

  const uploadStudentData = async (data) => {
    let payload = {
      "user_type":"student",
      "users": data
    }
     try {
      const response = await postData({ url: studentBulkUpload, body: payload }).unwrap().then((res) => {
        if (res?.status === 200) {
          toast.success(res?.message);
          navigate("/all-students");
        }
        
      });
    } catch (error) {
      console.log(error?.error?.error);
    }
  }

  const columns = [
      {
        header: 'Name',
        accessorKey: 'name',
        cell: info => (
          <span className={info.row.original.duplicate ? 'text-red-500' : ''}>
            {info.getValue()}
          </span>
        ),
      },
      {
        header: 'Email',
        accessorKey: 'email',
        cell: info => (
          <span className={info.row.original.duplicate ? 'text-red-500' : ''}>
            {info.getValue()}
          </span>
        ),
      },
      { header: 'Age', accessorKey: 'age' },
      { header: 'Gender', accessorKey: 'gender' },
      {
        header: 'Duplicate?',
        accessorKey: 'duplicate',
        cell: info =>
          info.getValue() ? (
            <span className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded">Yes</span>
          ) : (
            <span className="text-xs bg-green-100 text-green-600 px-2 py-1 rounded">No</span>
          ),
      },
      {
        header: 'Action',
        accessorKey: 'id',
      },
    ];
  
  return (
    <div>
      {isSubmitting && <Loading />}
      {!isSubmitting && 
        <BulkUsersUpload columns={columns} uploadData={uploadStudentData} excelUrl={import.meta.env.VITE_STUDENT_UPLOAD_EXCEL_FILE} dataKeys={studentKeys} compareKeys={["email", "name"]} />
      }
    </div>
  )
}

export default BulkUploadStudentList
