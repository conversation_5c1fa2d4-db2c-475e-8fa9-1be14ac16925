import React, { useEffect, useState } from 'react'
import BulkUsersUpload from '@/components/teachersStudents/BulkUsersUpload';
import { setBreadcrumbs, setPageTitle } from '@/store/common/commonSlice';
import { useDispatch } from 'react-redux';
import { schoolsBulkUpload } from '@/constant/apiUrls';
import { usePostDataMutation } from '@/store/api/apiSlice';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import { LoaderCircle } from 'lucide-react';
import Loading from '@/components/Loading';
const BulkUploadSchoolsList = () => {
  const dispatch = useDispatch();
  const [loader, setLoader] = useState(false);
  const [postData, { isLoading: isSubmitting }] = usePostDataMutation();
  const navigate = useNavigate();
  useEffect(() => {
    const breadCrumb = [
      { label: "Schools", path: "/all-schools" },
      { label: "Schools Bulk Upload", path: "#" }
    ];
    dispatch(setBreadcrumbs(breadCrumb));
    dispatch(setPageTitle({
      title: "Schools Bulk Upload",
      isBackButton: true
    }));
  }, [dispatch]);

  const [teacherKeys, setStudentKeys] = useState(Object.keys({
    "country_id": null,
    "name": "",
    "name_jp": "",
    "city": "",
    "ward": "",
    "prefecture": "",
    "full_address": "",
    "postal_code": "",
    "contact_phone": "",
    "contact_email": "",
    "logo": "",
    "school_type": "",
    "school_level": "",
    "established_year": "",
    "is_active": false
  }));

  const uploadSchoolData = async (data) => {
      setLoader(true);
      let payload = {
        "user_type":"school",
        "schools": data
      }

       try {
        const response = await postData({ url: schoolsBulkUpload, body: payload }).unwrap().then((res) => {
          if (res?.status === 200) {
            toast.success(res?.data?.message);
            setLoader(false);
            navigate("/all-schools");
          }
          
        });
      } catch (error) {
        console.log(error?.error?.error);
      }
    }

     

    const columns = [
    {
      header: 'Name',
      accessorKey: 'name',
      cell: info => (
        <span className={info.row.original.duplicate ? 'text-red-500' : ''}>
          {info.getValue()}
        </span>
      ),
    },
    {
      header: 'Email',
      accessorKey: 'contact_email',
      cell: info => (
        <span className={info.row.original.duplicate ? 'text-red-500' : ''}>
          {info.getValue()}
        </span>
      ),
    },
    { header: 'Age', accessorKey: 'established_year' },
    { header: 'Gender', accessorKey: 'contact_phone' },
    {
      header: 'Duplicate?',
      accessorKey: 'duplicate',
      cell: info =>
        info.getValue() ? (
          <span className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded">Yes</span>
        ) : (
          <span className="text-xs bg-green-100 text-green-600 px-2 py-1 rounded">No</span>
        ),
    },
    {
      header: 'Action',
      accessorKey: 'id',
      // The cell implementation will be provided by BulkUsersUpload component
    },
  ];

  return (
    <div>
      {isSubmitting && <Loading />}
      {!isSubmitting &&  
        <BulkUsersUpload uploadData={uploadSchoolData} columns={columns} excelUrl={import.meta.env.VITE_TEACHER_UPLOAD_EXCEL_FILE} dataKeys={teacherKeys} compareKeys={["contact_email", "name"]} />
      }
    </div>

  )
}

export default BulkUploadSchoolsList
