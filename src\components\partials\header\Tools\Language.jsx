
import { Fragment, useState } from "react";
import { Listbox, Transition } from "@headlessui/react";
import Icon from "@/components/ui/Icon";

// Language component definition
const Language = () => {
  // Your component implementation here
  // For example:
  const [selectedLang, setSelectedLang] = useState({
    value: "en",
    label: "English",
    image: "https://flagcdn.com/w40/us.png" // Changed to use an external flag image
  });

  const languages = [
    {
      value: "en",
      label: "English",
      image: "https://flagcdn.com/w40/us.png" // Changed to use an external flag image
    },
    {
      value: "bn",
      label: "Bangla",
      image: "https://flagcdn.com/w40/bd.png" // Changed to use an external flag image
    },
    {
      value: "hi",
      label: "Hindi",
      image: "https://flagcdn.com/w40/in.png" // Changed to use an external flag image
    },
    {
      value: "es",
      label: "Spanish",
      image: "https://flagcdn.com/w40/es.png" // Changed to use an external flag image
    }
  ];

  return (
    <div className="relative">
      <Listbox value={selectedLang} onChange={setSelectedLang}>
        <Listbox.Button className="flex items-center">
          <div className="h-6 w-8 overflow-hidden border border-slate-200">
            <img src={selectedLang.image} alt={selectedLang.label} className="h-full w-full object-cover" />
          </div>
          <Icon icon="heroicons-outline:chevron-down" className="text-slate-500 text-sm ml-1" />
        </Listbox.Button>
        <Transition
          as={Fragment}
          leave="transition ease-in duration-100"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <Listbox.Options className="absolute z-10 mt-2 min-w-[120px] right-0 rtl:left-0 rtl:right-auto overflow-hidden rounded-md bg-white dark:bg-slate-800 shadow-lg ring-1 ring-slate-900/10 dark:ring-slate-700/10">
            {languages.map((item, i) => (
              <Listbox.Option key={i} value={item}>
                {({ selected }) => (
                  <div
                    className={`${
                      selected
                        ? "bg-slate-100 dark:bg-slate-700 dark:bg-opacity-70 text-slate-900 dark:text-white"
                        : "text-slate-600 dark:text-slate-300"
                    } relative cursor-pointer select-none py-2 px-3 flex items-center`}
                  >
                    <div className="h-5 w-7 overflow-hidden border border-slate-200">
                      <img src={item.image} alt={item.label} className="h-full w-full object-cover" />
                    </div>
                    <span className="ml-2 rtl:mr-2">{item.label}</span>
                  </div>
                )}
              </Listbox.Option>
            ))}
          </Listbox.Options>
        </Transition>
      </Listbox>
    </div>
  );
};

export default Language;
