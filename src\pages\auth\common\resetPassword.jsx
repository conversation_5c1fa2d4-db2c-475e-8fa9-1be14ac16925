import React, { useState } from "react";
import { Formik, Form, ErrorMessage } from "formik";
import * as Yup from "yup";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import InputFieldIcon from "@/components/ui/formik-form/InputFieldWithIcon";
import InputFieldPasswordWithIcon from "@/components/ui/formik-form/InputFieldPasswordWithIcon";
import Button from "@/components/ui/Button";
import Cookies from "js-cookie";
import { usePostDataMutation } from "@/store/api/apiSlice";
import succesMessageIcon from "@/assets/images/all-img/successIcon.svg";
import { handleLogin, logOut } from "@/store/api/auth/authSlice";
import { set } from "react-hook-form";

const validationSchema = Yup.object().shape({
  name: Yup.string().required("Name is required"),
  password: Yup.string().required("Password is required"),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref("password")], "Passwords must match")
    .required("Confirm Password is required"),
});

const resetPassword = () => {
  const [postData, { isLoading }] = usePostDataMutation();
  const [showSuccess, setShowSuccess] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const userInfo = Cookies.get("userInfo");
  const parsedUserInfo = userInfo ? JSON.parse(userInfo) : {};
  const name = parsedUserInfo?.name || "";

  const handleSubmit = async (values, { setSubmitting }) => {
    console.log("values", values);

    try {
      let payload = {
        name: values.name,
        new_password: values.password,
      };
      const response = await postData({
        url: "/update-password",
        body: payload,
      });
      if (response?.data?.status == 200) {
        setShowSuccess(true);
        toast.success(response?.data?.message);
        dispatch(logOut());
        setTimeout(() => {
          navigate("/");
        }, 1000);
      }
    } catch (error) {
      toast.error(error.message);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <>
      {!showSuccess ? (
        <Formik
          initialValues={{
            name: name,
            password: "",
            confirmPassword: "",
          }}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting, errors, touched }) => (
            <Form className="space-y-4">
              <InputFieldIcon
                name="name"
                type="text"
                placeholder="Name"
                icon="iconamoon:profile-light"
                value={name}
                readOnly
                error={errors.name && touched.name && errors.name}
              />

              <InputFieldPasswordWithIcon
                name="password"
                type="password"
                placeholder="Enter your password"
                icon="mdi-light:lock"
                error={errors.password && touched.password && errors.password}
              />
              <InputFieldPasswordWithIcon
                name="confirmPassword"
                type="password"
                placeholder="Enter your confirm password"
                icon="mdi-light:lock"
                error={
                  errors.confirmPassword &&
                  touched.confirmPassword &&
                  errors.confirmPassword
                }
              />
              <div className="my-3">
                <Button
                  type="submit"
                  text="Submit"
                  className="bg-secondary-200 block w-full text-center h-[48px] text-black-600 hover:bg-primary-900 hover:text-white"
                  isLoading={isLoading || isSubmitting}
                />
              </div>
            </Form>
          )}
        </Formik>
      ) : (
        <div className="text-center mt-4 flex justify-center w-full p-20">
          <div className="text-center">
            <img
              src={succesMessageIcon}
              alt="successLogo"
              className="mx-auto mb-3"
            />
            <h3 className="text-green-600 text-2xl">
              Reset password successfully
            </h3>
          </div>
        </div>
      )}
    </>
  );
};

export default resetPassword;
