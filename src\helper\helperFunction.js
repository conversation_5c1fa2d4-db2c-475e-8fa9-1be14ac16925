import { setUserPrimaryInfo } from "@/store/common/commonSlice";
import Cookies from "js-cookie";
import { useDispatch } from "react-redux";
import DefaultProfile from "@/assets/DefaultProfile.svg";


export const getProfileInformationFromCookies = (dispatch) => {
    let userInfo = JSON.parse(Cookies.get("userInfo"));
    userInfo = {
        name: userInfo?.name,
        profile_image: userInfo?.profile_image
    }
    dispatch(setUserPrimaryInfo(userInfo));
    // return userInfo;
};

export const handleBack = () => {
    window.history.back();
};

export const setCookieWithExpiry = (expiryDays) => {
    return expiryDays / (60 * 60 * 24);
};

export const formatDateToISO = (dateInput) => {
    const date = new Date(dateInput);
    const offset = date.getTimezoneOffset() * 60000;
    const localDate = new Date(date.getTime() - offset);
    return localDate.toISOString().split("T")[0];
};

// export const replaceUploadUrl = (imageUrl) => {
//     const uploadUrl = import.meta.env.VITE_UPLOADS_URL + '/' + imageUrl;
//     return uploadUrl
// }

//Updated replaceUploadUrl images
export const replaceUploadUrl = (imageUrl) => {
    if (!imageUrl) return DefaultProfile;
    const uploadUrl = import.meta.env.VITE_IMAGE_URL + '/' + imageUrl;
    return uploadUrl;
};


export const updateProfileInformation = (values) => {
    return new Promise((resolve, reject) => {
        try {
            let infoData = Cookies.get("userInfo");
            infoData = JSON.parse(infoData);
            infoData.name = values.name;
            infoData.profile_image = values.profile_image;
            Cookies.set("userInfo", JSON.stringify(infoData), { expires: 1 });
            resolve(infoData);
        } catch (error) {
            reject(error);
        }
    });
};

