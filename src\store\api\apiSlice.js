import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import Cookies from 'js-cookie';

const API_URL = import.meta.env.VITE_API_URL;

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: API_URL,
    prepareHeaders: (headers) => {
      const token = Cookies.get('_token');
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Data'],
  endpoints: (builder) => ({
    fetchData: builder.query({
      query: (url) => ({ url, method: 'GET' }),
      transformResponse: (response, meta) => ({
        ...response,
        status: meta?.response?.status,
      }),
      providesTags: ['Data'],
    }),
    postData: builder.mutation({
      query: ({ url, body }) => ({
        url,
        method: 'POST',
        body,
        // headers: { 'Content-Type': 'application/json' }, // Explicit JSON header
      }),
      transformResponse: (response, meta) => ({
        ...response,
        status: meta?.response?.status,
      }),
      invalidatesTags: ['Data'],
    }),
    postDataWithFile: builder.mutation({
      query: ({ url, body }) => {
        return {
          url,
          method: 'POST',
          body: body,
          headers: {  
            // 'Content-Type': 'multipart/form-data',
          },
        };
      },
      transformResponse: (response, meta) => ({
        ...response,
        status: meta?.response?.status,
      }),
      invalidatesTags: ['Data'],
    }),
    putData: builder.mutation({
      query: ({ url, body }) => ({
        url,
        method: 'PUT',
        body,
        // headers: { 'Content-Type': 'application/json' },
      }),
      transformResponse: (response, meta) => ({
        ...response,
        status: meta?.response?.status,
      }),
      invalidatesTags: ['Data'],
    }),
    putDataWithFile: builder.mutation({
      query: ({ url, body }) => {
        return {
          url,
          method: 'PUT',
          body: body,
          headers: {  
            // 'Content-Type': 'multipart/form-data',
          },
        };
      },
      transformResponse: (response, meta) => ({
        ...response,
        status: meta?.response?.status,
      }),
      invalidatesTags: ['Data'],
    }),
    patchData: builder.mutation({
      query: ({ url, body }) => ({
        url,
        method: 'PATCH',
        body,
        // headers: { 'Content-Type': 'application/json' },
      }),
      transformResponse: (response, meta) => ({
        ...response,
        status: meta?.response?.status,
      }),
      invalidatesTags: ['Data'],
    }),
    patchDataWithFile: builder.mutation({
      query: ({ url, body }) => ({
        url,
        method: 'PATCH',
        body, // Send raw body
      }),
      transformResponse: (response, meta) => ({
        ...response,
        status: meta?.response?.status,
      }),
      invalidatesTags: ['Data'],
    }),
    deleteData: builder.mutation({
      query: ({ url }) => ({
        url,
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
      }),
      transformResponse: (response, meta) => ({
        ...response,
        status: meta?.response?.status,
      }),
      invalidatesTags: ['Data'],
    }),
    getAdminList: builder.query({
      query: () => ({ url: '/get-admin-list', method: 'GET' }),
      providesTags: ['Data'],
    }),
    getSchoolAdminList: builder.query({
      query: () => ({ url: '/get-school-admin-list', method: 'GET' }),
      providesTags: ['Data'],
    }),
  }),
});

export const { 
  useFetchDataQuery, 
  usePostDataMutation, 
  usePostDataWithFileMutation,
  usePutDataMutation, 
  usePutDataWithFileMutation,
  usePatchDataMutation, 
  usePatchDataWithFileMutation,
  useDeleteDataMutation,
  useGetAdminListQuery,
  useGetSchoolAdminListQuery,
} = apiSlice;
