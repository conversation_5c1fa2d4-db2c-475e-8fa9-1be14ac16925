import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { apiSlice } from '@/store/api/apiSlice';
import { toast } from 'react-toastify';

/**
 * Custom hook for fetching a single data item by ID
 * @returns {Object} - Functions and state for fetching single data
 */
const useSingleData = () => {
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  /**
   * Fetch data by ID from a specific endpoint
   * @param {string} endpoint - API endpoint path (without ID)
   * @param {string|number} id - ID of the item to fetch
   * @returns {Promise<Object|null>} - The fetched data or null if error
   */
  const fetchById = async (endpoint, id) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await dispatch(
        apiSlice.endpoints.fetchData.initiate(`${endpoint}/${id}`)
      ).unwrap();
      
      if (response?.status === 200) {
        setData(response.data);
        return response.data;
      } else {
        const errorMsg = response?.message || 'Failed to fetch data';
        setError(errorMsg);
        toast.error(errorMsg);
        return null;
      }
    } catch (err) {
      const errorMsg = err?.data?.message || 'Error fetching data';
      setError(errorMsg);
      toast.error(errorMsg);
      console.error('Error fetching data:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    fetchById,
    isLoading,
    error,
    data,
  };
};

export default useSingleData;